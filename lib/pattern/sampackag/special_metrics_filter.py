import sys
sys.path.append('../')
import numpy as np
import openpyxl
from xml.sax.saxutils import escape
import os
from pandas import json_normalize
import pandas as pd
from decimal import Decimal, ROUND_HALF_UP
import xml.etree.ElementTree as ET
from datetime import datetime
import math
from db_handler.db_connector import getCustomerPayTypeGroupsList, menuMasterTableResult, menuServiceTypeTableResult, assignedMenuModelsTableResult, assignedMenuOpcodesTableResult, MPISetupTableResult, MPIOpcodesTableResult


store_id = os.environ.get('store_id')
advisor_set = os.environ.get('advisor')
tech_set = os.environ.get('technician')
realm = os.environ.get('realm')
working_days = float(os.environ.get('working_days'))

s_date_env = os.environ.get('start_date')
e_date_env = os.environ.get('end_date')

s_year, s_month, s_date = map(int, s_date_env.split('-'))
e_year, e_month, e_date = map(int, e_date_env.split('-'))
s_date_f = (s_year, s_month, s_date)
e_date_f = (e_year, e_month, e_date)

start_date = datetime(*s_date_f)
end_date = datetime(*e_date_f)

# Handling multiple advisor and technician filter
if ',' in advisor_set:
    advisor = set(x.strip() for x in advisor_set.split(','))
else:
    advisor = {advisor_set.strip()}

if ',' in tech_set:
    tech = set(x.strip() for x in tech_set.split(','))
else:
    tech = {tech_set.strip()}


# Using this function to round the values to next integer
def round_off(n, decimals=0):
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal('1'), rounding=ROUND_HALF_UP) / multiplier)

#Function used for checking zero sales
def zero_sales_check(df, columns):
    total_sum = df[columns].sum().sum()
    return total_sum == 0

columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']

retail_flag_DB_connect = getCustomerPayTypeGroupsList()
retail_flag = retail_flag_DB_connect.getCustomerPayTypeList()


# Storing menu_master table from DB to a data frame
menu_master_db_connect = menuMasterTableResult()
menu_master_df = menu_master_db_connect.getTableResult()
#print("menu_master_df  --- ", menu_master_df)

# Storing menu_service_type table from DB to data frame
menu_service_type_db_connect = menuServiceTypeTableResult()
menu_service_type_df = menu_service_type_db_connect.getTableResult()

# Storing assigned_menu_models table from DB to data frame
assigned_menu_models_db_connect = assignedMenuModelsTableResult()
assigned_menu_models_df = assigned_menu_models_db_connect.getTableResult()

# Storing assigned_menu_opcodes table from DB to data frame
assigned_menu_opcodes_db_connect = assignedMenuOpcodesTableResult()
assigned_menu_opcodes_df = assigned_menu_opcodes_db_connect.getTableResult()

MPI_setup_db_connect = MPISetupTableResult()
MPI_setup_df = MPI_setup_db_connect.getTableResult()

MPI_opcodes_db_connect = MPIOpcodesTableResult()
mpi_opcodes = MPI_opcodes_db_connect.getTableResult()



# Read the all_revenue_details.csv and store it as a data frame
all_revenue_details = pd.read_csv('../Output/all_revenue_details.csv', na_values=[], keep_default_na=False)

all_revenue_details['closeddate'] = pd.to_datetime(all_revenue_details['closeddate'], errors= 'coerce')

all_revenue_details_df = all_revenue_details[
    (all_revenue_details['closeddate'] >= start_date) &
    (all_revenue_details['closeddate'] <= end_date)
    ]

columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']

# Replace empty strings or spaces with NaN
all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)

# Convert to numeric
all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))

all_adv_tech_df = all_revenue_details_df[
    (all_revenue_details_df['department'] == 'Service') & 
    (all_revenue_details_df['hide_ro'] != True)
    ]
# Finding Total RO count for calculating the % of Vehicles Serviced value on KPI Scorecard C
all_adv_tech_df = all_adv_tech_df.copy()  # Create a deep copy of filtered_df to avoid the warning
all_adv_tech_df['unique_ro_number'] = all_adv_tech_df['ronumber'].astype(str) + '_' + all_adv_tech_df['closeddate'].astype(str)
all_adv_tech_ro = set(all_adv_tech_df['unique_ro_number'])

# Initializing new data frame to filter only required advisor and technician
filtered_df = all_revenue_details_df

filtered_df = filtered_df[
    (filtered_df['department'] == 'Service') & 
    (filtered_df['hide_ro'] != True)
    ]

filtered_df = filtered_df.copy()  # Create a deep copy of filtered_df to avoid the warning
# RO number with different closeddate will be considered as different RO, joining RO number and closeddate to find out the unique RO number
filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)


if not filtered_df.empty:
    combined_revenue_details = filtered_df.copy()
    combined_revenue_details['group'] = pd.Series(dtype="string")

    # Define customer and warranty pay types dynamically
    if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
        customer_pay_types = {'C'}
        warranty_pay_types = {'W', 'F', 'M', 'E'}
    elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
        customer_pay_types = {'C', 'M'}
        warranty_pay_types = {'W', 'F', 'E'}
    elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
        customer_pay_types = {'C', 'E'}
        warranty_pay_types = {'W', 'F', 'M'}
    elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
        customer_pay_types = {'C', 'E', 'M'}
        warranty_pay_types = {'W', 'F'}

    # Create a temporary version for zero check without modifying the original data
    temp_revenue_details = combined_revenue_details.copy()
    temp_revenue_details.loc[temp_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0

    # Iterate through each unique RO number
    for ro_number in combined_revenue_details['unique_ro_number'].unique():
        ro_specific_rows = temp_revenue_details[temp_revenue_details['unique_ro_number'] == ro_number]

        ro_specific_rows_C = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(customer_pay_types)]
        ro_specific_rows_W = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(warranty_pay_types)]

        zero_sales_C = zero_sales_check(ro_specific_rows_C, columns_to_check)
        zero_sales_W = zero_sales_check(ro_specific_rows_W, columns_to_check)

        if not ro_specific_rows_C.empty and not zero_sales_C:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'C'
        elif not ro_specific_rows_W.empty and not zero_sales_W:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'W'
        else:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'I'
    combined_revenue_details.to_csv('../Output/combined_revenue_details.csv')
        
    # Apply filters based on the conditions
    if advisor == {'all'} and tech == {'all'}:
        # No filtering needed
        matching_ro_numbers = combined_revenue_details['unique_ro_number'].unique()
    elif advisor != {'all'} and tech == {'all'}:
        # Filter based on advisor only
        matching_ro_numbers =  combined_revenue_details.loc[combined_revenue_details['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
    elif advisor == {'all'} and tech != {'all'}:
        # Filter based on tech only
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
        
    elif advisor != {'all'} and tech != {'all'}:
        # Filter based on both advisor and tech
        matching_ro_numbers = combined_revenue_details.loc[(all_revenue_details_df['serviceadvisor'].astype(str).isin(advisor)) & 
            (all_revenue_details_df['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()
    # Applying the Advisor and tech filter conditions
    combined_revenue_details = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(matching_ro_numbers)]
    combined_revenue_details = combined_revenue_details.reset_index(drop=True)
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    combined_revenue_details.to_csv('../Output/combined_revenue_details_new.csv')

    combined_revenue_details_for_weekly_chart = combined_revenue_details.copy()

    combined_revenue_details_for_mpi = combined_revenue_details.copy()
    combined_revenue_details_for_menu = combined_revenue_details.copy()
    
    #combined_revenue_details_for_menu = combined_revenue_details.copy()

    # Calculate results
    Scorecard_10_CP = combined_revenue_details.loc[combined_revenue_details['group'] == 'C', 'unique_ro_number'].nunique()
    Scorecard_10_Wty = combined_revenue_details.loc[combined_revenue_details['group'] == 'W', 'unique_ro_number'].nunique()
    Scorecard_10_Int = combined_revenue_details.loc[combined_revenue_details['group'] == 'I', 'unique_ro_number'].nunique()
    all_unique_ros = Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int
    

    Total_Count_below_60k = 0
    Total_Count_above_60k = 0
    
    if not combined_revenue_details.empty:
    # Coverting the data frame to dictionary for applying conditions
        all_revenue_details_list = combined_revenue_details.to_dict('records')

        # Removing the jobs with paytypegroup other than C, M, E and excluding the ros with mileage as None
        total_revenue_details_C = [
        row for row in all_revenue_details_list
        if (row['group'] == 'C') and (row['paytypegroup'] in {'C', 'E', 'M'}) and not 
            (row['mileage'] is None)  # Use 'is None' for checking None type
        
        ]

        total_revenue_details_C_df = pd.DataFrame(total_revenue_details_C)
        total_revenue_details_C_df = total_revenue_details_C_df[
        ~((total_revenue_details_C_df['lbrsale'].fillna(0) == 0) &
        (total_revenue_details_C_df['lbrsoldhours'].fillna(0) == 0) &
        (total_revenue_details_C_df['prtextendedsale'].fillna(0) == 0) &
        (total_revenue_details_C_df['prtextendedcost'].fillna(0) == 0))
        ]

        total_revenue_details_C_df.to_csv('../Output/total_revenue_details_C_df.csv')
        
        # Identifying the available menus from menu master table
        menu_names = set(menu_master_df['menu_name'])

        # storing the list of available default menu
        default_menu_series = menu_master_df[menu_master_df['is_default'].astype(
            int) == 1]['menu_name']

        # Identifying the default menu name
        if not default_menu_series.empty:
            default_menu = default_menu_series.iloc[0]
        else:
            default_menu = np.nan

        # Checking whether models are assigned to the available menu
        if not assigned_menu_models_df.empty:

            # Create a mapping from 'model' to 'menu_name'
            model_to_menu_map = assigned_menu_models_df.set_index('model')[
                'menu_name'].to_dict()
            # Map the 'model' column in Menu_Opportunity_list_df to the 'mapped_menu'
            total_revenue_details_C_df['mapped_menu'] = total_revenue_details_C_df['model'].map(
                model_to_menu_map).fillna(default_menu)

        else:
            # if no model maaping available all models mapped to default menu
            total_revenue_details_C_df['mapped_menu'] = default_menu

        # Iitializing the menu impacted ROs as set
        ro_with_item_count_more_than_1 = set()
        # if no menus are added, all values under menu sales will be 0
        if menu_names:
            for name in menu_names:
                total_revenue_details_C_df[name] = ''

            # Create a dictionary to map service_type_id to service_type using menu_service_type table
            service_type_mapping = menu_service_type_df.set_index(
                'id')['service_type'].to_dict()

            # Create a set of menu names from the menu_master table only if it is available on the total_revenue_details_C_df columns
            menu_names = [
                name for name in menu_names if name in total_revenue_details_C_df.columns]

            # Iterate over each row in Menu_Opportunity_list_df
            for i, row in total_revenue_details_C_df.iterrows():
                mileage = row['mileage']

                # Initialize menu_names columns to NaN
                total_revenue_details_C_df.loc[i, menu_names] = np.nan

                # Find rows in menu_master_df where mileage is within the range
                matching_menus = menu_master_df[(menu_master_df['range_from'] <= mileage) & (
                    menu_master_df['range_to'] >= mileage)]

                for _, menu_row in matching_menus.iterrows():
                    menu_name = menu_row['menu_name']
                    
                    #print(" --- ",menu_name)
                    service_type_id = menu_row['service_type_id']
                    item_count = menu_row['items']

                    # Check if the menu_name column exists in Menu_Opportunity_list_df
                    if menu_name in total_revenue_details_C_df.columns:
                        # Map service_type_id to service_type
                        service_type = service_type_mapping.get(service_type_id, None)

                        # Update the value of the column in Menu_Opportunity_list_df with service_type
                        if service_type:
                            total_revenue_details_C_df.at[i, menu_name] = service_type

                            total_revenue_details_C_df.at[i,
                                                        menu_name + '_items'] = int(item_count)

        total_revenue_details_C_df.to_csv('../Output/total_revenue_details11.csv')

        total_revenue_details_C_df['lbrsale'] = pd.to_numeric(total_revenue_details_C_df['lbrsale'], errors='coerce')
        total_revenue_details_C_df['lbrsoldhours'] = pd.to_numeric(total_revenue_details_C_df['lbrsoldhours'], errors='coerce')
        total_revenue_details_C_df['prtextendedsale'] = pd.to_numeric(total_revenue_details_C_df['prtextendedsale'], errors='coerce')
        total_revenue_details_C_df['prtextendedcost'] = pd.to_numeric(total_revenue_details_C_df['prtextendedcost'], errors='coerce')
        
        total_revenue_details_C_df = total_revenue_details_C_df[
        ~((total_revenue_details_C_df['lbrsale'].fillna(0) == 0) &
        (total_revenue_details_C_df['lbrsoldhours'].fillna(0) == 0) &
        (total_revenue_details_C_df['prtextendedsale'].fillna(0) == 0) &
        (total_revenue_details_C_df['prtextendedcost'].fillna(0) == 0))
        ]
        
        # Based on mileage and lbrsoldhours, splitting the customer pay ROs into two list for calculating the total RO count
        total_revenue_details_C_df_Below_60k = total_revenue_details_C_df[
            # (total_revenue_details_C_df['lbrsoldhours'].astype(float) >= 0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (total_revenue_details_C_df['mileage'].astype(int) < 60000)]
        total_revenue_details_C_df_Above_60k = total_revenue_details_C_df[
            # (total_revenue_details_C_df['lbrsoldhours'].astype(float) >= 0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (total_revenue_details_C_df['mileage'].astype(int) >= 60000)]
            
        total_ro_count_below_60k = total_revenue_details_C_df_Below_60k['unique_ro_number'].nunique()
        total_ro_count_above_60k = total_revenue_details_C_df_Above_60k['unique_ro_number'].nunique()

        perc_of_business_below_60k = round_off((total_ro_count_below_60k / (total_ro_count_below_60k + total_ro_count_above_60k)) * 100)
        perc_of_business_above_60k = round_off((total_ro_count_above_60k / (total_ro_count_below_60k + total_ro_count_above_60k)) * 100)

        
        # Based on the jobs on each RO, splitting the Customer Pay ROs into different list, One Line RO and Multi Line RO
        value_counts = total_revenue_details_C_df['unique_ro_number'].value_counts()
        one_line_ROs = value_counts[value_counts == 1].index
        One_Line_RO_Details = total_revenue_details_C_df[(total_revenue_details_C_df['unique_ro_number'].isin(one_line_ROs))]
        Multi_Line_RO_Details = total_revenue_details_C_df[~total_revenue_details_C_df['unique_ro_number'].isin(one_line_ROs)]
        
        # # The below One Line code added for achieving the task 3638
        # One_Line_RO_Details = One_Line_RO_Details[One_Line_RO_Details['opcategory'] != 'N/A']
        
        available_menu = set(One_Line_RO_Details['mapped_menu'])
        #print("available_menu  -- ",available_menu)

        #if any(value is None or value == '' for value in available_menu):
        # if any(not math.isnan(value) for value in available_menu):
        if any(value and (not isinstance(value, float) or not math.isnan(value)) for value in available_menu):
        # Identifying the menu impacted One Line RO. If the item count more than 1 remove it from One Line list and add them into Multi Line
            for menu_map in available_menu:
                One_Line_RO_Details_with_menu = One_Line_RO_Details[One_Line_RO_Details[menu_map].notna()]
                #print(One_Line_RO_Details_with_menu)
                for i, row in One_Line_RO_Details_with_menu.iterrows():
                    opcode = row['lbropcode']
                    mapped_menu = row['mapped_menu']
                    service_type_name = row[mapped_menu]
                    service_id = next(key for key, value in service_type_mapping.items() if value == service_type_name)

                    current_menu_opcodes = assigned_menu_opcodes_df[assigned_menu_opcodes_df['menu_name'].astype(str) == mapped_menu]
                    current_menu_opcodes_set = set(current_menu_opcodes['menu_opcode'])
                    
                    if mapped_menu in One_Line_RO_Details_with_menu.columns:
                        item_count = row[mapped_menu + '_items']
                        
                        if int(item_count) > 1:
                            matching_opcode_row = assigned_menu_opcodes_df[(assigned_menu_opcodes_df['menu_opcode'] == opcode) & 
                                                                        (assigned_menu_opcodes_df['service_type'] == service_id) & 
                                                                        (assigned_menu_opcodes_df['menu_name'] == mapped_menu)]
                            #print("matching_opcode_row  ---- ", matching_opcode_row)
                            if not matching_opcode_row.empty:
                                Multi_Line_RO_Details = pd.concat([Multi_Line_RO_Details, pd.DataFrame([row])], ignore_index=True)
                                One_Line_RO_Details = One_Line_RO_Details.drop(index=i)


        # Based on mileage and lbrsoldhours value, splitting the One Line and Multi line list into two, Below and Above 60k
        One_Line_RO_Details_below60k = One_Line_RO_Details[
            # (One_Line_RO_Details['lbrsoldhours'] >= 0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (One_Line_RO_Details['mileage'].astype(int) < 60000)]
        One_Line_RO_Details_above60k = One_Line_RO_Details[
            # (One_Line_RO_Details['lbrsoldhours'].astype(float) >= 0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (One_Line_RO_Details['mileage'].astype(int) >= 60000)]
        Multi_Line_RO_Details_below60k = Multi_Line_RO_Details[
            # (Multi_Line_RO_Details['lbrsoldhours'].astype(float) >= 0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (Multi_Line_RO_Details['mileage'].astype(int) < 60000)]
        Multi_Line_RO_Details_above60k  = Multi_Line_RO_Details[
            # (Multi_Line_RO_Details['lbrsoldhours'].astype(float) >=0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (Multi_Line_RO_Details['mileage'].astype(int) >= 60000)]
        #Multi_Line_RO_Details_above60k  = Multi_Line_RO_Details[(Multi_Line_RO_Details['mileage'].astype(int) >= 60000)]
        One_Line_RO_Details_below60k.to_csv('../Output/One_Line_RO_Details_below60k.csv')
        One_Line_RO_Details_above60k.to_csv('../Output/One_Line_RO_Details_above60k.csv')
        Multi_Line_RO_Details_below60k.to_csv('../Output/Multi_Line_RO_Details_below60k.csv')
        Multi_Line_RO_Details_above60k.to_csv('../Output/Multi_Line_RO_Details_above60k.csv')
        
        # One Line RO count calculation
        one_line_ro_count_below_60k = One_Line_RO_Details_below60k.shape[0]
        
        one_line_ro_count_above_60k = One_Line_RO_Details_above60k.shape[0]

        # Multi_line RO count calculation
        multi_line_ro_count_below_60k = Multi_Line_RO_Details_below60k['unique_ro_number'].nunique()
        multi_line_ro_count_above_60k = Multi_Line_RO_Details_above60k['unique_ro_number'].nunique()

        # Calculating the One Line RO percentage
        perc_of_one_line_below_60k = round_off((one_line_ro_count_below_60k / total_ro_count_below_60k) * 100)
        perc_of_one_line_above_60k = round_off((one_line_ro_count_above_60k / total_ro_count_above_60k) * 100)

        # 948 - CP 1 Line RO count - FOPC_DV_0156
        One_Line_Mileage_Under_60k = one_line_ro_count_below_60k
        One_Line_Mileage_Over_60k = one_line_ro_count_above_60k
        One_Line_Total_Shop = One_Line_Mileage_Under_60k + One_Line_Mileage_Over_60k

        # 923 - CP 1-Line-RO Count Percentage
        # Calculating the One Line RO percentage
        perc_of_one_line_below_60k = round_off(((one_line_ro_count_below_60k / total_ro_count_below_60k) * 100), 2)
        perc_of_one_line_above_60k = round_off(((one_line_ro_count_above_60k / total_ro_count_above_60k) * 100), 2)
        perc_of_one_line_total_shop = round_off(((One_Line_Total_Shop / Scorecard_10_CP) * 100), 2)

        # 1354 - CP Muliti Line RO Count - FOPC_DV_0159
        Multi_Line_Mileage_Under_60k = multi_line_ro_count_below_60k
        Multi_Line_Mileage_Over_60k = multi_line_ro_count_above_60k
        Multi_Line_Total_Shop = Multi_Line_Mileage_Under_60k + Multi_Line_Mileage_Over_60k

        # 1355 - Multi-Line-RO Count Percentage
        perc_of_multi_line_below_60k = round_off(((Multi_Line_Mileage_Under_60k / total_ro_count_below_60k) * 100), 2)
        perc_of_multi_line_above_60k = round_off(((Multi_Line_Mileage_Over_60k / total_ro_count_above_60k) * 100), 2)
        perc_of_multi_line_total_shop = round_off(((Multi_Line_Total_Shop / Scorecard_10_CP) * 100), 2)

    # 1357 - Average RO Open Days
    # Filter the DataFrame to keep only those rows with the minimum 'opendate' for each 'unique_ro_number'
    combined_revenue_details['min_opendate'] = combined_revenue_details.groupby('unique_ro_number')['opendate'].transform('min')
    combined_revenue_details['open_days'] = (pd.to_datetime(combined_revenue_details['closeddate']) - pd.to_datetime(combined_revenue_details['min_opendate'])).dt.days

    filtered_df_with_sales = filtered_df[
        ~((pd.to_numeric(filtered_df['lbrsale'], errors='coerce').fillna(0) == 0) &
            (pd.to_numeric(filtered_df['lbrsoldhours'], errors='coerce').fillna(0) == 0) &
            (pd.to_numeric(filtered_df['prtextendedsale'], errors='coerce').fillna(0) == 0) &
            (pd.to_numeric(filtered_df['prtextendedcost'], errors='coerce').fillna(0) == 0))
        ]

    filtered_df_with_sales['min_opendate'] = filtered_df_with_sales.groupby('unique_ro_number')['opendate'].transform('min')
    filtered_df_with_sales['open_days'] = (pd.to_datetime(filtered_df_with_sales['closeddate']) - pd.to_datetime(filtered_df_with_sales['min_opendate'])).dt.days

    all_revenue_C = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'C']
    all_revenue_M = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'M']
    all_revenue_E = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'E']
    all_revenue_W = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'W']
    all_revenue_F = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'F']
    all_revenue_I = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'I']
    all_revenue_C.to_csv('../Output/OpenDays_C.csv')

    
    ro_count_for_combined = len(set(combined_revenue_details['unique_ro_number']))
    ro_count_for_C = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'C']['unique_ro_number']))
    ro_count_for_M = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'M']['unique_ro_number']))
    ro_count_for_E = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'E']['unique_ro_number']))
    ro_count_for_W = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'W']['unique_ro_number']))
    ro_count_for_F = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'F']['unique_ro_number']))
    ro_count_for_I = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'I']['unique_ro_number']))

    all_revenue_without_duplicates_for_open_days_C = all_revenue_C.loc[all_revenue_C.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)
    all_revenue_without_duplicates_for_open_days_W = all_revenue_W.loc[all_revenue_W.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)
    all_revenue_without_duplicates_for_open_days_I = all_revenue_I.loc[all_revenue_I.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)
    all_revenue_without_duplicates_for_open_days_E = all_revenue_E.loc[all_revenue_E.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)
    all_revenue_without_duplicates_for_open_days_M = all_revenue_M.loc[all_revenue_M.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)
    all_revenue_without_duplicates_for_open_days_F = all_revenue_F.loc[all_revenue_F.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)
    all_revenue_without_duplicates_for_open_days_C.to_csv('../Output/all_revenue_without_duplicates_for_open_days_C.csv')

    open_days_sum_C = all_revenue_without_duplicates_for_open_days_C['open_days'].sum()
    open_days_sum_W = all_revenue_without_duplicates_for_open_days_W['open_days'].sum()
    open_days_sum_I = all_revenue_without_duplicates_for_open_days_I['open_days'].sum()
    open_days_sum_E = all_revenue_without_duplicates_for_open_days_E['open_days'].sum()
    open_days_sum_M = all_revenue_without_duplicates_for_open_days_M['open_days'].sum()
    open_days_sum_F = all_revenue_without_duplicates_for_open_days_F['open_days'].sum()

    # print("ro_count_for_C  - ",ro_count_for_C)
    # print("ro_count_for_W  - ",ro_count_for_W)
    # print("ro_count_for_I  - ",ro_count_for_I)
    # print("ro_count_for_E  - ",ro_count_for_E)
    # print("ro_count_for_M  - ",ro_count_for_M)
    # print("ro_count_for_F  - ",ro_count_for_F)


    avg_days_open_C = 0
    if ro_count_for_C != 0:
        avg_days_open_C = round_off((open_days_sum_C / ro_count_for_C),2)
    avg_days_open_W = 0

    if ro_count_for_W != 0:
        avg_days_open_W = round_off((open_days_sum_W / ro_count_for_W),2)
    
    avg_days_open_I = 0
    if ro_count_for_I != 0:
        avg_days_open_I = round_off((open_days_sum_I / ro_count_for_I),2)

    avg_days_open_E = 0
    if ro_count_for_E != 0:
        avg_days_open_E = round_off((open_days_sum_E / ro_count_for_E),2)
    
    avg_days_open_F = 0
    if ro_count_for_F != 0:
        avg_days_open_F = round_off((open_days_sum_F / ro_count_for_F),2)
    
    avg_days_open_M = 0
    if ro_count_for_M != 0:
        avg_days_open_M = round_off((open_days_sum_M / ro_count_for_M),2)


    # 930 - CP Parts to Labor Ratio - FOPC_DV_0162
    #Filtering only CP job details
    list_of_paytypegroup_C = combined_revenue_details[combined_revenue_details['paytypegroup'].isin(['C', 'M', 'E']) & (combined_revenue_details['group'] == 'C')].to_dict('records')
    # Coverting it to data frame
    total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)
    total_CP_revenue_details_df = total_CP_revenue_details_df[
    ~((total_CP_revenue_details_df['lbrsale'].fillna(0) == 0) &
    (total_CP_revenue_details_df['lbrsoldhours'].fillna(0) == 0) &
    (total_CP_revenue_details_df['prtextendedsale'].fillna(0) == 0) &
    (total_CP_revenue_details_df['prtextendedcost'].fillna(0) == 0))
    ]
    total_CP_revenue_details_df.to_csv('../Output/Total_Revenue_Details.csv')

    lbr_sale_total = 0
    prt_ext_sale_total = 0

    if not total_CP_revenue_details_df.empty:
        lbr_sale_total = pd.to_numeric(total_CP_revenue_details_df['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_total = pd.to_numeric(total_CP_revenue_details_df['prtextendedsale'], errors='coerce').fillna(0).sum()

    else:
        print(" No data vailable for KPI Scorecard A calculation")

    CP_Parts_to_Labor_Ratio = round_off((prt_ext_sale_total / lbr_sale_total),2)
    # print("CP_Parts_to_Labor_Ratio  --- ",CP_Parts_to_Labor_Ratio)
    #print("lbr_sale_total  --- ",lbr_sale_total)

    # 935 - Labor Sold Hours Percentage By Pay Type - FOPC_DV_0163
    All_Sold_Hours = pd.to_numeric(combined_revenue_details['lbrsoldhours'], errors= 'coerce').fillna(0).sum()
    labor_sold_hours_C = 0
    if not all_revenue_C.empty:
        labor_sold_hours_C = round_off((pd.to_numeric(all_revenue_C['lbrsoldhours']).fillna(0).sum()),2)
    
    Labor_Sold_Hours_Percentage_C = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_C = round_off(((labor_sold_hours_C / All_Sold_Hours) * 100))
    
    labor_sold_hours_W = 0
    if not all_revenue_W.empty:
        labor_sold_hours_W = round_off((pd.to_numeric(all_revenue_W['lbrsoldhours']).fillna(0).sum()),2)
    
    Labor_Sold_Hours_Percentage_W = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_W = round_off(((labor_sold_hours_W / All_Sold_Hours) * 100))
    
    labor_sold_hours_I = 0
    if not all_revenue_I.empty:
        labor_sold_hours_I = round_off((pd.to_numeric(all_revenue_I['lbrsoldhours']).fillna(0).sum()),2)
    
    Labor_Sold_Hours_Percentage_I = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_I = round_off(((labor_sold_hours_I / All_Sold_Hours) * 100))
   
    labor_sold_hours_E = 0
    if not all_revenue_E.empty:
        labor_sold_hours_E = round_off((pd.to_numeric(all_revenue_E['lbrsoldhours']).fillna(0).sum()),2)

    Labor_Sold_Hours_Percentage_E = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_E = round_off(((labor_sold_hours_E / All_Sold_Hours) * 100))

    labor_sold_hours_M = 0
    if not all_revenue_M.empty:
        labor_sold_hours_M = round_off((pd.to_numeric(all_revenue_M['lbrsoldhours']).fillna(0).sum()),2)
    
    Labor_Sold_Hours_Percentage_M = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_M = round_off(((labor_sold_hours_M / All_Sold_Hours) * 100))

    labor_sold_hours_F = 0
    if not all_revenue_F.empty:
        labor_sold_hours_F = round_off((pd.to_numeric(all_revenue_F['lbrsoldhours']).fillna(0).sum()),2)
    
    Labor_Sold_Hours_Percentage_F = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_F = round_off(((labor_sold_hours_F / All_Sold_Hours) * 100))
    

    total_CP_revenue_details_comp = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'COMPETITIVE']
    total_CP_revenue_details_maint = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'MAINTENANCE']
    total_CP_revenue_details_rep = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'REPAIR']

    lbr_sale_comp = 0
    prt_ext_sale_comp = 0
    if not total_CP_revenue_details_comp.empty:
        lbr_sale_comp = pd.to_numeric(total_CP_revenue_details_comp['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_comp = pd.to_numeric(total_CP_revenue_details_comp['prtextendedsale'], errors='coerce').fillna(0).sum()
    
    CP_Parts_to_Labor_Ratio_Comp = 0
    if lbr_sale_comp != 0:
        CP_Parts_to_Labor_Ratio_Comp = round_off((prt_ext_sale_comp / lbr_sale_comp),2)

    lbr_sale_maint = 0
    prt_ext_sale_maint = 0
    if not total_CP_revenue_details_maint.empty:
        lbr_sale_maint = pd.to_numeric(total_CP_revenue_details_maint['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_maint = pd.to_numeric(total_CP_revenue_details_maint['prtextendedsale'], errors='coerce').fillna(0).sum()
    
    CP_Parts_to_Labor_Ratio_maint = 0
    if lbr_sale_maint != 0:
        CP_Parts_to_Labor_Ratio_maint = round_off((prt_ext_sale_maint / lbr_sale_maint),2)
    
    lbr_sale_rep = 0
    prt_ext_sale_rep = 0
    if not total_CP_revenue_details_rep.empty:
        lbr_sale_rep = pd.to_numeric(total_CP_revenue_details_rep['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_rep = pd.to_numeric(total_CP_revenue_details_rep['prtextendedsale'], errors='coerce').fillna(0).sum()
    
    CP_Parts_to_Labor_Ratio_rep = 0
    if lbr_sale_rep != 0:
        CP_Parts_to_Labor_Ratio_rep = round_off((prt_ext_sale_rep / lbr_sale_rep),2)

    # 1316 - MPI Penetration Percentage - FOPC_DV_0166
    all_revenue_details_list = combined_revenue_details_for_mpi.to_dict('records')

    # Identifying the FRH value updated on UI from DB table
    frh_value = 0
    if not MPI_setup_df.empty:
        frh_value = MPI_setup_df.loc[MPI_setup_df['is_active'] == '1', 'frh'].iloc[0]

    #Removing the jobs with 0 sales and department body shop and filter paytype group with C and W
    MPI_Opportunity_list = [
        row for row in all_revenue_details_list
        if not (
            (
                row['lbrsale'] == 0 and 
                row['lbrsoldhours'] == 0 and 
                row['prtextendedsale'] == 0 and 
                row['prtextendedcost'] == 0) or
                row['department'] == 'Body Shop' or 
                # row['opcategory'] == 'N/A' or 
                row['hide_ro'] == True or
                row['mileage'] <= 1000 
        ) and row['group'] in {'C', 'W'} and row['paytypegroup'] in {'C', 'M', 'E', 'W', 'F'}
    ]

    print('MPI_Opportunity_list----->++++++++++++++++++',len(MPI_Opportunity_list))
    # Get distinct values of the 'ronumber' column
    MPI_Opportunity_list_df = pd.DataFrame(MPI_Opportunity_list)
    # Excluding the rows with customer name balnk or None
    MPI_Opportunity_list_df['customer_name'] = MPI_Opportunity_list_df['customer_name'].replace('', np.nan)
    MPI_Opportunity_list_df = MPI_Opportunity_list_df[MPI_Opportunity_list_df['customer_name'].notna()]
    MPI_Opportunity_list_df.to_csv('../Output/MPI_Opportunity_list_df.csv')

    Opportunities = 0
    if not MPI_Opportunity_list_df.empty:
        #Same ronumber with different closeddate will be considered as two different ronumber
        MPI_Opportunity_list_df['unique_ronumber'] = MPI_Opportunity_list_df['ronumber'].astype(str) + '_' + MPI_Opportunity_list_df['closeddate'].astype(str)
        distinct_ronumbers = set(MPI_Opportunity_list_df['unique_ronumber'])
        Opportunities = len(distinct_ronumbers)
    # print(" +++++++++++++++++",mpi_opcodes)
    # MPI Completed calculation
    # Identifying the ronumber with MPI jobs
    ronumbers_with_mpi = {row['ronumber'] for row in all_revenue_details_list if row.get('lbropcode','').strip() in mpi_opcodes}

    # Filtering all jobs of ronumber, if MPI opcodes are available in any of its job
    filtered_rows = [row for row in all_revenue_details_list if row['ronumber'] in ronumbers_with_mpi]

    # Removing jobs with zero sales and body shop jobs and jobs with paytype group 'I' 
    final_filtered_rows = [
        row for row in filtered_rows 
        if not (
            (row['lbrsale'] == 0 and row['lbrsoldhours'] == 0 and row['prtextendedsale'] == 0 and row['prtextendedcost'] == 0) 
            or row['department'] == 'Body Shop' or row['paytypegroup'] == 'I' or row['hide_ro'] == True or row['group'] == 'I' 
            #or 
            # row['opcategory'] == 'N/A'
        )
    ]
    #Coverting the list to data frame
    final_filtered_rows_df = pd.DataFrame(final_filtered_rows)
    final_filtered_rows_df.to_csv('../Output/MPI_final_filtered_rows_df.csv')
    Completed_MPI_ROs = {}
    Completed_MPI_ROs_Perc = 0

    
    if not final_filtered_rows_df.empty:
        # Moving final list of jobs to a set to identify the count of completed MPI ROs
        #Same ronumber with different closeddate will be considered as two different ronumber
        final_filtered_rows_df['unique_onumber'] = final_filtered_rows_df['ronumber'].astype(str)+'_'+final_filtered_rows_df['closeddate'].astype(str)
        Completed_MPI_ROs = set(final_filtered_rows_df['unique_onumber'])

        Completed_MPI_ROs_Perc = round_off(((len(Completed_MPI_ROs) / Opportunities) * 100),2)

    # 1317 - Menu Penetration Percentage
    menu_sold = 0
    menu_jobs = 0
    menu_sold_perc = 0
    menu_pportunities = 0
    if not combined_revenue_details.empty:
        # Coverting the data frame to dictionary for applying conditions
        all_revenue_details_list = combined_revenue_details.to_dict('records')
        # Removing the jobs with 0 sales and department body shop and filter paytype group with C and W
        Menu_Opportunity_list = [
            row for row in all_revenue_details_list
            if not (
                (row['department'] == 'Body Shop') 
                
            ) and row['group'] in {'C', 'W'}
            and row['paytypegroup'] in {'C', 'M', 'E', 'W', 'F'}
            #) and row['paytypegroup'] in {'C', 'M', 'E', 'W', 'F'}
        ]

        # Coverting the list to data frame
        Menu_Opportunity_list_df = pd.DataFrame(Menu_Opportunity_list)
        #Menu_Opportunity_list_df = combined_revenue_details

        #Excluding the rows with mileage less than 1000 and customer_name is blank or None
        Menu_Opportunity_list_df = Menu_Opportunity_list_df[Menu_Opportunity_list_df['mileage'] > 1000]
        Menu_Opportunity_list_df['customer_name'] = Menu_Opportunity_list_df['customer_name'].replace('', np.nan)
        Menu_Opportunity_list_df = Menu_Opportunity_list_df[Menu_Opportunity_list_df['customer_name'].notna()]

        

        # Identifying the available menus from menu master table
        menu_names = set(menu_master_df['menu_name'])

        # storing the list of available default menu
        default_menu_series = menu_master_df[menu_master_df['is_default'].astype(
            int) == 1]['menu_name']


        # Identifying the default menu name
        if not default_menu_series.empty:
            default_menu = default_menu_series.iloc[0]
        else:
            default_menu = np.nan

        # Checking whether models are assigned to the available menu
        if not assigned_menu_models_df.empty:

            # Create a mapping from 'model' to 'menu_name'
            model_to_menu_map = assigned_menu_models_df.set_index('model')[
                'menu_name'].to_dict()
            # Map the 'model' column in Menu_Opportunity_list_df to the 'mapped_menu'
            Menu_Opportunity_list_df['mapped_menu'] = Menu_Opportunity_list_df['model'].map(
                model_to_menu_map).fillna(default_menu)

        else:
            # if no model maaping available all models mapped to default menu
            Menu_Opportunity_list_df['mapped_menu'] = default_menu

        # if no menus are added, all values under menu sales will be 0
        if menu_names:
            for name in menu_names:
                Menu_Opportunity_list_df[name] = ''

            # Create a dictionary to map service_type_id to service_type using menu_service_type table
            service_type_mapping = menu_service_type_df.set_index(
                'id')['service_type'].to_dict()

            # Create a set of menu names from the menu_master table
            menu_names = [
                name for name in menu_names if name in Menu_Opportunity_list_df.columns]

            # Iterate over each row in Menu_Opportunity_list_df
            for i, row in Menu_Opportunity_list_df.iterrows():
                mileage = row['mileage']

                # Initialize menu_names columns to NaN
                Menu_Opportunity_list_df.loc[i, menu_names] = np.nan

                # Find rows in menu_master_df where mileage is within the range
                matching_menus = menu_master_df[(menu_master_df['range_from'] <= mileage) & (
                    menu_master_df['range_to'] >= mileage)]

                for _, menu_row in matching_menus.iterrows():
                    menu_name = menu_row['menu_name']
                    service_type_id = menu_row['service_type_id']

                    # Check if the menu_name column exists in Menu_Opportunity_list_df
                    if menu_name in Menu_Opportunity_list_df.columns:
                        # Map service_type_id to service_type
                        service_type = service_type_mapping.get(service_type_id, None)

                        # Update the value of the column in Menu_Opportunity_list_df with service_type
                        if service_type:
                            Menu_Opportunity_list_df.at[i, menu_name] = service_type

                            Menu_Opportunity_list_df.at[i,
                                                        menu_name + '_id'] = service_type_id
            Menu_Opportunity_list_df.to_csv('../Output/total_revenue_details_C_W.csv')
            # menu calculation is applicable to menu only if the models in current data set are assigned to the menu. Else, all models are assigned to the default menu
            mapped_menus = set()
            for menus in Menu_Opportunity_list_df['mapped_menu']:
                mapped_menus.update(menus.split(','))

            individual_ro_counts = {
                menu: {'Basic': 0, 'Intermediate': 0, 'Major': 0} for menu in mapped_menus}

            for menu in mapped_menus:
                # Filter rows where 'mapped_menu' contains the current menu

                filtered_df_2 = Menu_Opportunity_list_df[Menu_Opportunity_list_df['mapped_menu'] == menu].copy(
                )

                opprtunity_ros_set = set(filtered_df_2['ronumber'])
                all_revenue_with_opprtunity_ros = combined_revenue_details[combined_revenue_details['ronumber'].isin(opprtunity_ros_set)]

                ronumbers_with_menu_opcodes = {
                        row['ronumber'] 
                        for _, row in all_revenue_with_opprtunity_ros.iterrows() 
                        if row.get('lbropcode', '').strip() in assigned_menu_opcodes_df.loc[assigned_menu_opcodes_df['menu_name'] == menu, 'menu_opcode'].values
                    }
                # Filter out rows where the current menu column is null
                filtered_df_2 = filtered_df_2.dropna(subset=[menu])
                # Filtering the rows with menu opcodes that assigned to the current menu
                # filtered_df_with_menu_opcodes = filtered_df_2[filtered_df_2['lbropcode'].str.upper().isin(
                #     assigned_menu_opcodes_df.loc[assigned_menu_opcodes_df['menu_name'] == menu, 'menu_opcode'])].copy()
                filtered_df_with_menu_opcodes = filtered_df_2[filtered_df_2['ronumber'].isin(
                    ronumbers_with_menu_opcodes)].copy()
                # Filtering the rows with menu opcodes that assigned to the current menu also filter only the rows that has paytypegroup C, E, and M
                #   ------- Below line code is temporary, currently considering only paytypegroup C, after optimization task completed by dev team, need to consdier C, E, M  ------
                # filtered_df_with_menu_opcodes_CP = filtered_df[(filtered_df['lbropcode'].str.upper().isin(
                    # assigned_menu_opcodes_df.loc[assigned_menu_opcodes_df['menu_name'] == menu, 'menu_opcode'])) & (filtered_df['paytypegroup'].isin({'C'}))].copy()
                filtered_df_with_menu_opcodes_CP = filtered_df_2[(filtered_df_2['lbropcode'].str.upper().isin(assigned_menu_opcodes_df.loc[assigned_menu_opcodes_df['menu_name'] == menu, 'menu_opcode'])) & (filtered_df_2['group'].isin({'C'})) & (filtered_df_2['paytypegroup'].isin({'C','M','E'}))].copy()
                


                # Get the unique ro count for each category for calculating upsell pottentials and pottentials hours
                for category in ['Basic', 'Intermediate', 'Major']:
                    # Filtering the list based on category and paytypegroup C, E, and M
                    #   ------- Below line code is temporary, currently considering only paytypegroup C, after optimization task completed by dev team, need to consdier C, E, M  ------
                    # category_filtered_df = filtered_df[(filtered_df[menu] == category) & (
                    #     filtered_df['paytypegroup'].isin({'C'}))].copy()
                    category_filtered_df = filtered_df_2[(filtered_df_2[menu] == category) & (filtered_df_2['group'].isin({'C'})) & (filtered_df_2['paytypegroup'].isin({'C','M','E'}))].copy()
                    # Same RO with different closed date will be considered as two individual ROs. Joining the ronumber and closeddate to identify the distinct ro count
                    category_filtered_df['unique_ronumber_category'] = category_filtered_df['ronumber'].astype(
                        str) + '_' + category_filtered_df['closeddate'].astype(str).copy()
                    unique_ro_count_for_cat = category_filtered_df['unique_ronumber_category'].nunique(
                    )
                    individual_ro_counts[menu][category] = unique_ro_count_for_cat

                # Same RO with different closed date will be considered as two individual ROs. Joining the ronumber and closeddate to identify the distinct ro count
                filtered_df_2['unique_ronumber'] = filtered_df_2['ronumber'].astype(
                    str) + '_' + filtered_df_2['closeddate'].astype(str).copy()
                # Identifying the ro count of data with menu opcodes
                filtered_df_2.to_csv('../Output/filtered_df_2.csv')
                filtered_df_with_menu_opcodes['unique_ronumber'] = filtered_df_with_menu_opcodes['ronumber'].astype(
                    str) + '_' + filtered_df_with_menu_opcodes['closeddate'].astype(str).copy()
                filtered_df_with_menu_opcodes_CP.to_csv('../Output/filtered_df_with_menu_opcodes_CP.csv')

                #Labor sale, Parts sale and sold hour calculation
                labor_sale_with_menu_opcodes = pd.to_numeric(filtered_df_with_menu_opcodes_CP['lbrsale']).fillna(0).sum(
                )
                parts_sale_with_menu_opcodes = pd.to_numeric(filtered_df_with_menu_opcodes_CP['prtextendedsale']).fillna(0).sum(
                )
                labor_parts_sale = labor_sale_with_menu_opcodes + parts_sale_with_menu_opcodes
                sold_hours_with_menu_opcodes = pd.to_numeric(filtered_df_with_menu_opcodes_CP['lbrsoldhours']).fillna(0).sum(
                )

                # sold_dollar += labor_parts_sale
                # sold_hours += sold_hours_with_menu_opcodes
                # Get the unique ro count
                unique_ro_count = filtered_df_2['unique_ronumber'].nunique()

                rocount_with_menu_opcodes = filtered_df_with_menu_opcodes['unique_ronumber'].nunique(
                )
                jobcount_with_menu_opcodes = filtered_df_with_menu_opcodes_CP.shape[0]

                # Add the unique ro count to the total
                menu_pportunities += unique_ro_count
                menu_sold += rocount_with_menu_opcodes
                menu_jobs += jobcount_with_menu_opcodes
            print(menu_sold," --- ",menu_pportunities)
            if menu_pportunities != 0:
                menu_sold_perc = round_off(((menu_sold / menu_pportunities) * 100), 2)
            else:
                menu_sold_perc = 0

print("CP 1-Line-RO Count - 948 >>>>")
print("Mileage Under 60k : ", One_Line_Mileage_Under_60k)
print("Mileage Over 60k : ", One_Line_Mileage_Over_60k)
print("Total Shop : ", One_Line_Total_Shop)
print("______________________________________________________________________")

print("Average RO Open Days - 1357 >>>>")
print("Customer Pay : ", avg_days_open_C)
print("Extended Service : ", avg_days_open_E)
print("Internal : ", avg_days_open_I)
print("Maintenance : ", avg_days_open_M)
print("Warranty : ", avg_days_open_W)
print("Factory Service Contract : ", avg_days_open_F)
print("______________________________________________________________________")

print("CP 1-Line-RO Count Percentage - 923 >>>>")
print("Mileage Under 60k : ", perc_of_one_line_below_60k)
print("Mileage Over 60k : ", perc_of_one_line_above_60k)
print("Total Shop : ", perc_of_one_line_total_shop)
print("______________________________________________________________________")

print("Multi-Line-RO Count - 1354 >>>>")
print("Mileage Under 60k : ", Multi_Line_Mileage_Under_60k)
print("Mileage Over 60k : ", Multi_Line_Mileage_Over_60k)
print("Total Shop : ", Multi_Line_Total_Shop)
print("______________________________________________________________________")

print("Multi-Line-RO Count Percentage - 1355 >>>>")
print("Mileage Under 60k : ", perc_of_multi_line_below_60k)
print("Mileage Over 60k : ", perc_of_multi_line_above_60k)
print("Total Shop : ", perc_of_multi_line_total_shop)
print("______________________________________________________________________")

print("CP Parts to Labor Ratio - 930 >>>>")
print("Parts to labor Ratio : P$", CP_Parts_to_Labor_Ratio," to L$1.00")
print("______________________________________________________________________")

print("Labor Sold Hours Percentage By Pay Type - 935 >>>>")
print("Customer Pay : ", Labor_Sold_Hours_Percentage_C)
print("Extended Service : ", Labor_Sold_Hours_Percentage_E)
print("Internal : ", Labor_Sold_Hours_Percentage_I)
print("Maintenance : ", Labor_Sold_Hours_Percentage_M)
print("Warranty : ", Labor_Sold_Hours_Percentage_W)
print("Factory Service Contract : ", Labor_Sold_Hours_Percentage_F)
print("______________________________________________________________________")

print("CP Parts to Labor Ratio By Category - 936 >>>>")
print("Competitive : P$", CP_Parts_to_Labor_Ratio_Comp, " to L$1.00")
print("Maintenance : P$", CP_Parts_to_Labor_Ratio_maint, " to L$1.00")
print("Repair : P$", CP_Parts_to_Labor_Ratio_rep, " to L$1.00")
print("______________________________________________________________________")

print("MPI Penetration Percentage - 1316 >>>>")
print("MPI Penetration Percentage : ", Completed_MPI_ROs_Perc)
print("______________________________________________________________________")

print("Menu Penetration Percentage - 1316 >>>>")
print("Menu Penetration Percentage : ", menu_sold_perc)


result_set =[{
"CP 1-Line-RO Count - 948" : None,
"Mileage Under 60k - 948" : One_Line_Mileage_Under_60k,
"Mileage Over 60k - 948" : One_Line_Mileage_Over_60k,
"Total Shop - 948" : One_Line_Total_Shop,
"Average RO Open Days - 1357" : None,
"Customer Pay - 1357" : avg_days_open_C,
"Extended Service - 1357" : avg_days_open_E,
"Internal - 1357" : avg_days_open_I,
"Maintenance - 1357" : avg_days_open_M,
"Warranty - 1357" : avg_days_open_W,
"Factory Service Contract - 1357" : avg_days_open_F,
"CP 1-Line-RO Count Percentage - 923" : None,
"Mileage Under 60k - 923" : perc_of_one_line_below_60k,
"Mileage Over 60k - 923" : perc_of_one_line_above_60k,
"Total Shop - 923" : perc_of_one_line_total_shop,
"Multi-Line-RO Count - 1354" : None,
"Mileage Under 60k - 1354" : Multi_Line_Mileage_Under_60k,
"Mileage Over 60k - 1354" : Multi_Line_Mileage_Over_60k,
"Total Shop - 1354" : Multi_Line_Total_Shop,
"Multi-Line-RO Count Percentage - 1355" : None,
"Mileage Under 60k - 1355" : perc_of_multi_line_below_60k,
"Mileage Over 60k - 1355" : perc_of_multi_line_above_60k,
"Total Shop - 1355" : perc_of_multi_line_total_shop,
"CP Parts to Labor Ratio - 930" : None,
"Parts to labor Ratio : P$" : CP_Parts_to_Labor_Ratio,
"Labor Sold Hours Percentage By Pay Type - 935" : None,
"Customer Pay - 935" : Labor_Sold_Hours_Percentage_C,
"Extended Service - 935" : Labor_Sold_Hours_Percentage_E,
"Internal - 935" : Labor_Sold_Hours_Percentage_I,
"Maintenance - 935" : Labor_Sold_Hours_Percentage_M,
"Warranty - 935" : Labor_Sold_Hours_Percentage_W,
"Factory Service Contract - 935" : Labor_Sold_Hours_Percentage_F,
"CP Parts to Labor Ratio By Category - 936" : None,
"Competitive : P$" : CP_Parts_to_Labor_Ratio_Comp,
"Maintenance : P$" : CP_Parts_to_Labor_Ratio_maint,
"Repair : P$" : CP_Parts_to_Labor_Ratio_rep,
"MPI Penetration Percentage - 1316" : None,
"MPI Penetration Percentage" : Completed_MPI_ROs_Perc,
"Menu Penetration Percentage - 1316" : None,
"Menu Penetration Percentage" : menu_sold_perc,

}]

header = list(result_set[0].keys())

path = "../Output/results_set.xlsx"

workbook = openpyxl.Workbook()

# Load an existing workbook if it exists, or create a new one if it doesn't
try:
    workbook = openpyxl.load_workbook(path)
except FileNotFoundError:
    workbook = openpyxl.Workbook()

# Define your sheet name
sheet_name = "Special_Metrics"

# Check if the sheet exists, and remove it if it does
if sheet_name in workbook.sheetnames:
    del workbook[sheet_name]

# Create a new sheet with the same name
worksheet1 = workbook.create_sheet(title=sheet_name)

# Write the header to the worksheet
worksheet1.append(header)

# Write the data to the worksheet
for data in result_set:
    row_data = [data[column] for column in header]
    worksheet1.append(row_data)

# Check if there is a default sheet ("Sheet" or "Sheet1") and remove it
default_sheet_name = "Sheet"  # or "Sheet1" depending on your Excel version
if default_sheet_name in workbook.sheetnames:
    del workbook[default_sheet_name]

# Save the workbook
workbook.save(path)

print("Special Metrics calculation completed successfully")

