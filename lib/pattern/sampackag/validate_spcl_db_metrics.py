# import math
# import sys
# import os
# import argparse

# # Add the project root to the Python path
# project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
# if project_root not in sys.path:
#     sys.path.insert(0, project_root)

# from datetime import datetime, timedelta
# from dateutil.relativedelta import relativedelta
# import pandas as pd
# import numpy as np
# from decimal import Decimal, ROUND_HALF_UP
# import json

# # Import your existing database connectors
# from lib.pattern.sampackag.db_handler.db_connector import (
#     getCustomerPayTypeGroupsList, menuMasterTableResult,
#     menuServiceTypeTableResult, assignedMenuModelsTableResult,
#     assignedMenuOpcodesTableResult, MPISetupTableResult,
#     MPIOpcodesTableResult, allRevenueDetailsCPOverview, allRevenueDetailsTable,totalDetailsTable
# )

# # Import config for accessing global configuration
# from lib.pattern.config import config

# def round_off(n, decimals=0):
#     """Round off numbers with proper decimal handling"""
#     multiplier = Decimal(10) ** decimals
#     if isinstance(n, float):
#         n = Decimal(str(n))
#     return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

# def zero_sales_check(df, columns):
#     """Check if all specified columns sum to zero"""
#     total_sum = df[columns].sum().sum()
#     return total_sum == 0

# def get_month_date_range_from_target(target_date_str):
#     """Get the start and end date for the target month"""
#     # Modified to handle YYYY-MM format
#     target_date = datetime.strptime(target_date_str, "%Y-%m")    
#     month_start = target_date.replace(day=1)    
#     month_end = month_start + relativedelta(months=1) - timedelta(days=1)    
#     return month_start, month_end

# def calculate_shop_supplies_details(combined_revenue_details, advisor=None):
#     """
#     Calculate shop supplies details matching the database function logic

#     Args:
#         combined_revenue_details: DataFrame with revenue details
#         advisor: List of service advisors or None for all

#     Returns:
#         dict: Shop supplies totals by month for different pay types
#     """
#     try:
#         # Import the database connector for total_details
#         # from db_handler.db_connector import totalDetailsTable

#         # Step 1: Filter base data similar to temp_data_all_revenue_details
#         base_filter = (
#             (combined_revenue_details['department'] == 'Service') &
#             (combined_revenue_details['hide_ro'] == '0')
#         )

#         # Apply advisor filter
#         if advisor is None or advisor == {'all'}:
#             advisor_filter = True
#         else:
#             advisor_filter = combined_revenue_details['serviceadvisor'].isin(advisor)

#         temp_data = combined_revenue_details[base_filter & advisor_filter].copy()

#         if temp_data.empty:
#             return initialize_empty_shop_supplies()
        
#         # Step 2: Create temp_all_revenue_details equivalent
#         # First part: opcategory != 'N/A' and has some revenue/cost
#         valid_ops = temp_data[
#             (temp_data['opcategory'] != 'N/A') &
#             ~((temp_data['lbrsale'].fillna(0) == 0) & 
#               (temp_data['lbrsoldhours'].fillna(0) == 0) & 
#               (temp_data['prtextendedsale'].fillna(0) == 0) & 
#               (temp_data['prtextendedcost'].fillna(0) == 0)) &
#             (temp_data['paytypegroup'].isin(['C', 'E', 'M', 'W', 'F', 'I']))
#         ].copy()
        
#         # Map paytype using paytype_retail_flag_setting logic
#         paytype_mapping = {'C': 'C', 'E': 'E', 'M': 'M', 'W': 'W', 'F': 'F', 'I': 'I'}
#         valid_ops['mapped_paytype'] = valid_ops['paytypegroup'].map(paytype_mapping)
        
#         # Second part: opcategory == 'N/A' or zero revenue/cost (mapped to 'I')
#         invalid_ops = temp_data[
#             (temp_data['opcategory'] == 'N/A') |
#             ((temp_data['lbrsale'].fillna(0) == 0) & 
#              (temp_data['lbrsoldhours'].fillna(0) == 0) & 
#              (temp_data['prtextendedsale'].fillna(0) == 0) & 
#              (temp_data['prtextendedcost'].fillna(0) == 0))
#         ].copy()
#         invalid_ops['mapped_paytype'] = 'I'
        
#         # Combine both parts
#         temp_all_revenue = pd.concat([valid_ops, invalid_ops], ignore_index=True)
        
#         # Step 3: Determine RO_Type based on database logic
#         temp_all_revenue['RO_Type'] = ''
        
#         # Get ROs with Customer pay type
#         customer_ros = temp_all_revenue[temp_all_revenue['mapped_paytype'] == 'C']['ronumber'].unique()
#         temp_all_revenue.loc[temp_all_revenue['ronumber'].isin(customer_ros), 'RO_Type'] = 'C'
        
#         # Get ROs with Warranty pay type (only if not already marked as Customer)
#         warranty_ros = temp_all_revenue[
#             (temp_all_revenue['mapped_paytype'] == 'W') & 
#             (temp_all_revenue['RO_Type'] == '')
#         ]['ronumber'].unique()
#         temp_all_revenue.loc[temp_all_revenue['ronumber'].isin(warranty_ros), 'RO_Type'] = 'W'
        
#         # Mark remaining as Internal
#         temp_all_revenue.loc[temp_all_revenue['RO_Type'] == '', 'RO_Type'] = 'I'
        
#         # Step 4: Get unique ROs with their types
#         unique_ros = temp_all_revenue[['ronumber', 'closeddate', 'RO_Type', 'store_id']].drop_duplicates()
        
#         # Step 5: Get shop supplies data from total_details table
#         try:
#             total_details_db_connect = totalDetailsTable()
#             total_details_df = total_details_db_connect.getTableResult()
#             print(f"Loaded {len(total_details_df)} records from total_details table")
#         except Exception as e:
#             print(f"Error loading total_details: {str(e)}")
#             print("Warning: totalshopsupply data not available. Using placeholder values.")
#             total_details_df = pd.DataFrame(columns=['ronumber', 'closeddate', 'store_id', 'totalshopsupply'])

#         # Join with shop supplies data
#         if not total_details_df.empty:
#             # Convert data types for proper joining
#             total_details_df['ronumber'] = total_details_df['ronumber'].astype(str)
#             total_details_df['store_id'] = total_details_df['store_id'].astype(str)
#             total_details_df['closeddate'] = pd.to_datetime(total_details_df['closeddate'])

#             # Ensure unique_ros has proper data types
#             unique_ros['ronumber'] = unique_ros['ronumber'].astype(str)
#             unique_ros['store_id'] = unique_ros['store_id'].astype(str)
#             unique_ros['closeddate'] = pd.to_datetime(unique_ros['closeddate'])

#             shop_supplies_data = pd.merge(
#                 total_details_df[['ronumber', 'closeddate', 'store_id', 'totalshopsupply']],
#                 unique_ros,
#                 on=['ronumber', 'closeddate', 'store_id'],
#                 how='inner'
#             )
#         else:
#             print("Warning: No shop supplies data available. Using placeholder values.")
#             shop_supplies_data = unique_ros.copy()
#             shop_supplies_data['totalshopsupply'] = 0
        
#         if shop_supplies_data.empty:
#             return initialize_empty_shop_supplies()
        
#         # Step 6: Convert closeddate to datetime and create month_year
#         shop_supplies_data['closeddate'] = pd.to_datetime(shop_supplies_data['closeddate'])
#         shop_supplies_data['month_year'] = shop_supplies_data['closeddate'].dt.strftime('%Y-%m')
#         shop_supplies_data['month_first_day'] = shop_supplies_data['closeddate'].dt.to_period('M').dt.start_time
        
#         # Step 7: Group by month and RO_Type, then sum shop supplies
#         monthly_grouped = shop_supplies_data.groupby(['month_first_day', 'RO_Type'])['totalshopsupply'].sum().reset_index()
        
#         if monthly_grouped.empty:
#             return initialize_empty_shop_supplies()
        
#         # Step 8: Pivot to get separate columns for each RO_Type
#         monthly_pivot = monthly_grouped.pivot(index='month_first_day', columns='RO_Type', values='totalshopsupply').fillna(0)
        
#         # Ensure all required columns exist
#         for col in ['C', 'W', 'I']:
#             if col not in monthly_pivot.columns:
#                 monthly_pivot[col] = 0
        
#         # Calculate combined totals
#         monthly_pivot['Combined'] = monthly_pivot['C'] + monthly_pivot['W'] + monthly_pivot['I']
        
#         # Sort by date descending (most recent first)
#         monthly_pivot = monthly_pivot.sort_index(ascending=False)
        
#         # Return the most recent month's data
#         if not monthly_pivot.empty:
#             latest_month = monthly_pivot.iloc[0]
#             return {
#                 "combined": round_off(latest_month['Combined'], 2),
#                 "customer_pay": round_off(latest_month['C'], 2),
#                 "warranty": round_off(latest_month['W'], 2),
#                 "internal": round_off(latest_month['I'], 2),
#                 "month": monthly_pivot.index[0].strftime('%Y-%m')
#             }
        
#         return initialize_empty_shop_supplies()
        
#     except Exception as e:
#         print(f"Error calculating shop supplies details: {str(e)}")
#         return initialize_empty_shop_supplies()

# def initialize_empty_shop_supplies():
#     """Initialize empty shop supplies structure"""
#     return {
#         "combined": 0.00,
#         "customer_pay": 0.00,
#         "warranty": 0.00,
#         "internal": 0.00,
#         "month": "No Data"
#     }

# def process_target_month_special_metrics(all_revenue_details_df, month_start, month_end, advisor, tech, 
#                                        retail_flag, customer_pay_types, warranty_pay_types, columns_to_check):
#     """Process special metrics data for the target month and return results"""
    
#     month_start = month_start.date()
#     month_end = month_end.date()   
#     print(f"Target month range: {month_start} to {month_end}")
    
#     # Filter data for the specific target month
#     month_data = all_revenue_details_df[
#         (all_revenue_details_df['closeddate'] >= month_start) &
#         (all_revenue_details_df['closeddate'] <= month_end)
#     ]
#     # print(f"Target month data shape++++++++++++++++++++++++++++++: {month_data.count()}")    
    
#     if month_data.empty:
#         return None
    
#     # Apply existing filtering logic
#     filtered_df = month_data[
#         (month_data['department'] == 'Service') & 
#         (month_data['hide_ro'] != True)
#     ]    
#     print(f"Filtered data shape ============================: {filtered_df.count()}")
#     if filtered_df.empty:
#         return None
    
#     filtered_df = filtered_df.copy()
#     filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)
    
#     # Initialize the combined revenue details with group assignment
#     combined_revenue_details = filtered_df.copy()
#     combined_revenue_details['group'] = pd.Series(dtype="string")
    
#     # Group assignment logic (same as original)
#     temp_revenue_details = combined_revenue_details.copy()
#     temp_revenue_details.loc[temp_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0

#     for ro_number in combined_revenue_details['unique_ro_number'].unique():
#         ro_specific_rows = temp_revenue_details[temp_revenue_details['unique_ro_number'] == ro_number]        
#         ro_specific_rows_C = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(customer_pay_types)]
#         ro_specific_rows_W = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(warranty_pay_types)]       
#         zero_sales_C = zero_sales_check(ro_specific_rows_C, columns_to_check)
#         zero_sales_W = zero_sales_check(ro_specific_rows_W, columns_to_check)        
        
#         if not ro_specific_rows_C.empty and not zero_sales_C:
#             combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'C'
#         elif not ro_specific_rows_W.empty and not zero_sales_W:
#             combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'W'
#         else:
#             combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'I'
    
#     # Apply filters based on advisor and tech conditions
#     if advisor == {'all'} and tech == {'all'}:
#         matching_ro_numbers = combined_revenue_details['unique_ro_number'].unique()
#     elif advisor != {'all'} and tech == {'all'}:
#         matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
#     elif advisor == {'all'} and tech != {'all'}:
#         matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
#     elif advisor != {'all'} and tech != {'all'}:
#         matching_ro_numbers = combined_revenue_details.loc[(combined_revenue_details['serviceadvisor'].astype(str).isin(advisor)) & 
#             (combined_revenue_details['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()
    
#     # Apply the advisor and tech filter conditions
#     combined_revenue_details = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(matching_ro_numbers)]
#     combined_revenue_details = combined_revenue_details.reset_index(drop=True)
#     combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    
#     # Calculate RO counts
#     Scorecard_10_CP = combined_revenue_details.loc[combined_revenue_details['group'] == 'C', 'unique_ro_number'].nunique()
#     Scorecard_10_Wty = combined_revenue_details.loc[combined_revenue_details['group'] == 'W', 'unique_ro_number'].nunique()
#     Scorecard_10_Int = combined_revenue_details.loc[combined_revenue_details['group'] == 'I', 'unique_ro_number'].nunique()
    
#     # Initialize default values
#     special_metrics_results = {
#         "one_line_metrics": {
#             "under_60k": 0,
#             "over_60k": 0,
#             "total_shop": 0,
#             "perc_under_60k": 0,
#             "perc_over_60k": 0,
#             "perc_total_shop": 0
#         },
#         "multi_line_metrics": {
#             "under_60k": 0,
#             "over_60k": 0,
#             "total_shop": 0,
#             "perc_under_60k": 0,
#             "perc_over_60k": 0,
#             "perc_total_shop": 0
#         },
#         "average_open_days": {
#             "customer_pay": 0,
#             "warranty": 0,
#             "internal": 0,
#             "extended_service": 0,
#             "maintenance": 0,
#             "factory_service": 0
#         },
#         "parts_to_labor_ratio": {
#             "overall": 0,
#             "competitive": 0,
#             "maintenance": 0,
#             "repair": 0
#         },
#         "labor_hours_percentage": {
#             "customer_pay": 0,
#             "warranty": 0,
#             "internal": 0,
#             "extended_service": 0,
#             "maintenance": 0,
#             "factory_service": 0
#         },
#         "mpi_penetration_percentage": 0,
#         "menu_penetration_percentage": 0,
#         "shop_supplies": initialize_empty_shop_supplies()
#     }
    
#     # Only proceed with calculations if we have customer pay data
#     if Scorecard_10_CP > 0:
#         special_metrics_results = calculate_special_metrics(
#             combined_revenue_details, customer_pay_types, warranty_pay_types, 
#             columns_to_check, Scorecard_10_CP
#         )
    
#     # Calculate shop supplies regardless of customer pay data
#     shop_supplies_results = calculate_shop_supplies_details(combined_revenue_details, advisor)
#     special_metrics_results["shop_supplies"] = shop_supplies_results
    
#     return {
#         "target_month": month_start.strftime('%Y-%m'),
#         "target_month_name": month_start.strftime('%B %Y'),
#         "total_ros": Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int,
#         "ro_counts": {
#             "customer_pay_ros": Scorecard_10_CP,
#             "warranty_ros": Scorecard_10_Wty,
#             "internal_ros": Scorecard_10_Int
#         },
#         "special_metrics": special_metrics_results
#     }

# def calculate_special_metrics(combined_revenue_details, customer_pay_types, warranty_pay_types, columns_to_check, total_cp_ros):
#     """Calculate all special metrics"""
    
#     # Filter for customer pay data with mileage information
#     all_revenue_details_list = combined_revenue_details.to_dict('records')
    
#     total_revenue_details_C = [
#         row for row in all_revenue_details_list
#         if (row['group'] == 'C') and (row['paytypegroup'] in customer_pay_types) and 
#         (row['mileage'] is not None)
#     ]
    
#     if not total_revenue_details_C:
#         return initialize_empty_metrics()
    
#     total_revenue_details_C_df = pd.DataFrame(total_revenue_details_C)
#     # total_revenue_details_C_df.to_csv('total_revenue_details_C.csv', index=False)
    
#     # Remove zero-sales rows
#     total_revenue_details_C_df = total_revenue_details_C_df[
#         ~((total_revenue_details_C_df['lbrsale'].fillna(0) == 0) &
#           (total_revenue_details_C_df['lbrsoldhours'].fillna(0) == 0) &
#           (total_revenue_details_C_df['prtextendedsale'].fillna(0) == 0) &
#           (total_revenue_details_C_df['prtextendedcost'].fillna(0) == 0))
#     ]
#     total_revenue_details_C_df.to_csv('total_revenue_details_C.csv', index=False)
#     # Convert numeric columns
#     numeric_cols = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
#     for col in numeric_cols:
#         total_revenue_details_C_df[col] = pd.to_numeric(total_revenue_details_C_df[col], errors='coerce').fillna(0)
    
#     # Calculate one-line and multi-line metrics
#     one_line_metrics, multi_line_metrics = calculate_line_ro_metrics(total_revenue_details_C_df, total_cp_ros)
    
#     # Calculate average open days
#     avg_open_days = calculate_average_open_days(combined_revenue_details)
    
#     # Calculate parts to labor ratio
#     parts_to_labor = calculate_parts_to_labor_ratio(total_revenue_details_C_df)
    
#     # Calculate labor hours percentage
#     labor_hours_perc = calculate_labor_hours_percentage(combined_revenue_details)
    
#     # Calculate MPI and Menu penetration (simplified versions)
#     mpi_penetration = calculate_mpi_penetration(combined_revenue_details)
#     menu_penetration = calculate_menu_penetration(combined_revenue_details)
    
#     return {
#         "one_line_metrics": one_line_metrics,
#         "multi_line_metrics": multi_line_metrics,
#         "average_open_days": avg_open_days,
#         "parts_to_labor_ratio": parts_to_labor,
#         "labor_hours_percentage": labor_hours_perc,
#         "mpi_penetration_percentage": mpi_penetration,
#         "menu_penetration_percentage": menu_penetration,
#         "shop_supplies": initialize_empty_shop_supplies()  # Will be calculated separately
#     }


# def calculate_line_ro_metrics(df, total_cp_ros):
#     """Calculate one-line and multi-line RO metrics"""
    
#     # Split by mileage
#     below_60k = df[df['mileage'].astype(int) < 60000]
#     above_60k = df[df['mileage'].astype(int) >= 60000]
    
#     total_ro_count_below_60k = below_60k['unique_ro_number'].nunique()
#     total_ro_count_above_60k = above_60k['unique_ro_number'].nunique()
    
#     # Identify one-line vs multi-line ROs
#     value_counts = df['unique_ro_number'].value_counts()
#     one_line_ROs = value_counts[value_counts == 1].index
    
#     One_Line_RO_Details = df[df['unique_ro_number'].isin(one_line_ROs)]
#     # print(f"Total one-line ROs vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv: {One_Line_RO_Details}")
#     Multi_Line_RO_Details = df[~df['unique_ro_number'].isin(one_line_ROs)]
    
#     # Split one-line and multi-line by mileage
#     one_line_below60k = One_Line_RO_Details[One_Line_RO_Details['mileage'].astype(int) < 60000]
#     one_line_above60k = One_Line_RO_Details[One_Line_RO_Details['mileage'].astype(int) >= 60000]
#     multi_line_below60k = Multi_Line_RO_Details[Multi_Line_RO_Details['mileage'].astype(int) < 60000]
#     multi_line_above60k = Multi_Line_RO_Details[Multi_Line_RO_Details['mileage'].astype(int) >= 60000]
#     # Calculate counts
#     one_line_count_below_60k = one_line_below60k.shape[0]
#     one_line_count_above_60k = one_line_above60k.shape[0]
#     multi_line_count_below_60k = multi_line_below60k['unique_ro_number'].nunique()
#     multi_line_count_above_60k = multi_line_above60k['unique_ro_number'].nunique()
    
#     one_line_total = one_line_count_below_60k + one_line_count_above_60k
#     multi_line_total = multi_line_count_below_60k + multi_line_count_above_60k
    
#     # Calculate percentages
#     one_line_perc_below_60k = round_off((one_line_count_below_60k / total_ro_count_below_60k) * 100, 2) if total_ro_count_below_60k > 0 else 0
#     one_line_perc_above_60k = round_off((one_line_count_above_60k / total_ro_count_above_60k) * 100, 2) if total_ro_count_above_60k > 0 else 0
#     one_line_perc_total = round_off((one_line_total / total_cp_ros) * 100, 2) if total_cp_ros > 0 else 0
    
#     multi_line_perc_below_60k = round_off((multi_line_count_below_60k / total_ro_count_below_60k) * 100, 2) if total_ro_count_below_60k > 0 else 0
#     multi_line_perc_above_60k = round_off((multi_line_count_above_60k / total_ro_count_above_60k) * 100, 2) if total_ro_count_above_60k > 0 else 0
#     multi_line_perc_total = round_off((multi_line_total / total_cp_ros) * 100, 2) if total_cp_ros > 0 else 0
    
#     # drilldown calculation vv
    
#     # CP 1-Line-RO Count 948
#     one_line_below60k_lbrsale = one_line_below60k['lbrsale'].sum()
#     one_line_below60k_lbrsoldhours = one_line_below60k['lbrsoldhours'].sum()

#     one_line_above60k_lbrsale = one_line_above60k['lbrsale'].sum()
#     one_line_above60k_lbrsoldhours = one_line_above60k['lbrsoldhours'].sum()

#     one_line_total_shop_lbrsale = one_line_below60k_lbrsale + one_line_above60k_lbrsale
#     one_line_total_shop_lbrsoldhours = one_line_below60k_lbrsoldhours + one_line_above60k_lbrsoldhours

#     # CP 1-Line-RO Count Percentage 923
#     # one_line_count_below_60k_perc =  
#     # one_line_count_above_60k_perc = Todo 
#     # one_line_total_shop_perc = Todo 

#     # one_line_count_below_60k_prec_ro_count = round_off((one_line_count_below_60k / total_ro_count_below_60k) * 100, 2) if total_ro_count_below_60k > 0 else 0
#     print(f"one_line_below60k_lbrsale vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv: {one_line_below60k_lbrsale}")
#     # Multi-Line-RO Count 1354



#     # end 
    
#     one_line_metrics = {
#         "under_60k": one_line_count_below_60k,
#         "over_60k": one_line_count_above_60k,
#         "total_shop": one_line_total,
#         "perc_under_60k": one_line_perc_below_60k,
#         "perc_over_60k": one_line_perc_above_60k,
#         "perc_total_shop": one_line_perc_total
#     }
    
#     multi_line_metrics = {
#         "under_60k": multi_line_count_below_60k,
#         "over_60k": multi_line_count_above_60k,
#         "total_shop": multi_line_total,
#         "perc_under_60k": multi_line_perc_below_60k,
#         "perc_over_60k": multi_line_perc_above_60k,
#         "perc_total_shop": multi_line_perc_total
#     }
    
#     return one_line_metrics, multi_line_metrics

# def calculate_average_open_days(combined_revenue_details):
#     """Calculate average open days by pay type"""
    
#     # Filter out zero-sales rows
#     filtered_df_with_sales = combined_revenue_details[
#         ~((pd.to_numeric(combined_revenue_details['lbrsale'], errors='coerce').fillna(0) == 0) &
#           (pd.to_numeric(combined_revenue_details['lbrsoldhours'], errors='coerce').fillna(0) == 0) &
#           (pd.to_numeric(combined_revenue_details['prtextendedsale'], errors='coerce').fillna(0) == 0) &
#           (pd.to_numeric(combined_revenue_details['prtextendedcost'], errors='coerce').fillna(0) == 0))
#     ]
    
#     # Calculate open days
#     filtered_df_with_sales['min_opendate'] = filtered_df_with_sales.groupby('unique_ro_number')['opendate'].transform('min')
#     filtered_df_with_sales['open_days'] = (pd.to_datetime(filtered_df_with_sales['closeddate']) - pd.to_datetime(filtered_df_with_sales['min_opendate'])).dt.days
    
#     # Group by pay types
#     pay_type_groups = {
#         'C': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'C'],
#         'W': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'W'],
#         'I': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'I'],
#         'E': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'E'],
#         'M': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'M'],
#         'F': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'F']
#     }
    
#     avg_days = {}
#     for pay_type, group_df in pay_type_groups.items():
#         if not group_df.empty:
#             # Get unique ROs with minimum open days
#             unique_ros = group_df.loc[group_df.groupby('unique_ro_number')['open_days'].idxmin()]
#             ro_count = len(set(group_df['unique_ro_number']))
#             avg_days[pay_type] = round_off(unique_ros['open_days'].sum() / ro_count, 2) if ro_count > 0 else 0
#         else:
#             avg_days[pay_type] = 0
    
#     return {
#         "customer_pay": avg_days.get('C', 0),
#         "warranty": avg_days.get('W', 0),
#         "internal": avg_days.get('I', 0),
#         "extended_service": avg_days.get('E', 0),
#         "maintenance": avg_days.get('M', 0),
#         "factory_service": avg_days.get('F', 0)
#     }

# def calculate_parts_to_labor_ratio(df):
#     """Calculate parts to labor ratio overall and by category"""
    
#     # Overall ratio
#     total_labor_sale = df['lbrsale'].sum()
#     total_parts_sale = df['prtextendedsale'].sum()
#     overall_ratio = round_off(total_parts_sale / total_labor_sale, 2) if total_labor_sale > 0 else 0
#     print('calculate_parts_to_labor_ratio=======================---->total_labor_sale ',total_labor_sale)
#     print('calculate_parts_to_labor_ratio=======================---->total_parts_sale ',total_parts_sale)
#     print('calculate_parts_to_labor_ratio=======================---->overall_ratio ',overall_ratio)
#     # By category
#     categories = {
#         'competitive': df[df['opcategory'] == 'COMPETITIVE'],
#         'maintenance': df[df['opcategory'] == 'MAINTENANCE'],
#         'repair': df[df['opcategory'] == 'REPAIR']
#     }
    
#     category_ratios = {}
#     for category, cat_df in categories.items():
#         if not cat_df.empty:
#             cat_labor = cat_df['lbrsale'].sum()
#             cat_parts = cat_df['prtextendedsale'].sum()
#             category_ratios[category] = round_off(cat_parts / cat_labor, 2) if cat_labor > 0 else 0
#         else:
#             category_ratios[category] = 0
    
#     return {
#         "overall": overall_ratio,
#         "competitive": category_ratios.get('competitive', 0),
#         "maintenance": category_ratios.get('maintenance', 0),
#         "repair": category_ratios.get('repair', 0)
#     }

# def calculate_labor_hours_percentage(combined_revenue_details):
#     """Calculate labor hours percentage by pay type"""
    
#     total_sold_hours = pd.to_numeric(combined_revenue_details['lbrsoldhours'], errors='coerce').fillna(0).sum()
    
#     pay_types = ['C', 'W', 'I', 'E', 'M', 'F']
#     pay_type_names = ['customer_pay', 'warranty', 'internal', 'extended_service', 'maintenance', 'factory_service']
    
#     percentages = {}
#     for pay_type, name in zip(pay_types, pay_type_names):
#         pay_type_df = combined_revenue_details[combined_revenue_details['paytypegroup'] == pay_type]
#         if not pay_type_df.empty:
#             pay_type_hours = pd.to_numeric(pay_type_df['lbrsoldhours'], errors='coerce').fillna(0).sum()
#             percentages[name] = round_off((pay_type_hours / total_sold_hours) * 100, 2) if total_sold_hours > 0 else 0
#         else:
#             percentages[name] = 0
    
#     return percentages

# def calculate_mpi_penetration(combined_revenue_details):
#     """Calculate MPI penetration percentage"""
#     try:
#         # Apply the same data preprocessing as in the original code
#         # Set sales columns to 0 for rows where opcategory is 'N/A'
#         columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
#         combined_revenue_details_for_mpi = combined_revenue_details.copy()
#         combined_revenue_details_for_mpi.loc[combined_revenue_details_for_mpi['opcategory'] == 'N/A', columns_to_check] = 0

#         # Get MPI setup data
#         MPI_setup_db_connect = MPISetupTableResult()
#         MPI_setup_df = MPI_setup_db_connect.getTableResult()

#         # Get MPI opcodes
#         mpi_opcodes_db = MPIOpcodesTableResult()
#         mpi_opcodes = mpi_opcodes_db.getTableResult()
#         print("Debug - calculate_mpi_penetration: mpi_opcodes",mpi_opcodes)
#         # 1316 - MPI Penetration Percentage - FOPC_DV_0166
#         all_revenue_details_list = combined_revenue_details_for_mpi.to_dict('records')

#         # Identifying the FRH value updated on UI from DB table
#         frh_value = 0
#         if not MPI_setup_df.empty:
#             frh_value = MPI_setup_df.loc[MPI_setup_df['is_active'] == '1', 'frh'].iloc[0]

#         #Removing the jobs with 0 sales and department body shop and filter paytype group with C and W
#         MPI_Opportunity_list = [
#             row for row in all_revenue_details_list
#             if not (
#                 (
#                     row['lbrsale'] == 0 and
#                     row['lbrsoldhours'] == 0 and
#                     row['prtextendedsale'] == 0 and
#                     row['prtextendedcost'] == 0) or
#                     row['department'] == 'Body Shop' or
#                     # row['opcategory'] == 'N/A' or
#                     row['hide_ro'] == True or
#                     str(row['mileage']) <= '1000'
#             ) and row['group'] in {'C', 'W'} and row['paytypegroup'] in {'C', 'M', 'E', 'W', 'F'}
#         ]


#         # Get distinct values of the 'ronumber' column
#         # MPI_Opportunity_list_df = pd.DataFrame(MPI_Opportunity_list)
#         # MPI_Opportunity_list_df = MPI_Opportunity_list_df[MPI_Opportunity_list_df['mileage'] > 1000]
#          # Get distinct values of the 'ronumber' column

#         MPI_Opportunity_list_df = pd.DataFrame(MPI_Opportunity_list)

#         # Excluding the rows with customer name balnk or None

#         MPI_Opportunity_list_df['customer_name'] = MPI_Opportunity_list_df['customer_name'].replace('', np.nan)

#         MPI_Opportunity_list_df = MPI_Opportunity_list_df[MPI_Opportunity_list_df['customer_name'].notna()]
#         # Create Output directory if it doesn't exist
#         # output_dir = '../Output'
#         # if not os.path.exists(output_dir):
#         #     os.makedirs(output_dir)
#         print('..........................................BEFORE')
#         MPI_Opportunity_list_df.to_csv('MPI_Opportunity_list_df.csv', index=False)
#         print('..........................................AFTER')

#         Opportunities = 0
#         if not MPI_Opportunity_list_df.empty:
#             #Same ronumber with different closeddate will be considered as two different ronumber
#             MPI_Opportunity_list_df['unique_ronumber'] = MPI_Opportunity_list_df['ronumber'].astype(str) + '_' + MPI_Opportunity_list_df['closeddate'].astype(str)
#             distinct_ronumbers = set(MPI_Opportunity_list_df['unique_ronumber'])
#             Opportunities = len(distinct_ronumbers)
#         # print(" +++++++++++++++++",mpi_opcodes)
#         # MPI Completed calculation
#         # Identifying the ronumber with MPI jobs

#         # ronumbers_with_mpi = {row['ronumber'] for row in MPI_Opportunity_list if row.get('lbropcode','').strip() in mpi_opcodes}//NEW



#         ronumbers_with_mpi = {row['ronumber'] for row in all_revenue_details_list if row.get('lbropcode','').strip() in mpi_opcodes}
#         print("Debug - calculate_mpi_penetration: ronumbers_with_mpi",len(all_revenue_details_list))

#         # Filtering all jobs of ronumber, if MPI opcodes are available in any of its job
#         filtered_rows = [row for row in all_revenue_details_list if row['ronumber'] in ronumbers_with_mpi]

#         # Removing jobs with zero sales and body shop jobs and jobs with paytype group 'I'
#         final_filtered_rows = [
#             row for row in filtered_rows
#             if not (
#                 (row['lbrsale'] == 0 and row['lbrsoldhours'] == 0 and row['prtextendedsale'] == 0 and row['prtextendedcost'] == 0)
#                 or row['department'] == 'Body Shop' or row['paytypegroup'] == 'I' or row['hide_ro'] == True or row['group'] == 'I'
#                 #or
#                 # row['opcategory'] == 'N/A'
#             )
#         ]
#         #Coverting the list to data frame
#         final_filtered_rows_df = pd.DataFrame(final_filtered_rows)
#         final_filtered_rows_df.to_csv('../Output/MPI_final_filtered_rows_df.csv')
#         Completed_MPI_ROs = {}
#         Completed_MPI_ROs_Perc = 0


#         if not final_filtered_rows_df.empty:
#             # Moving final list of jobs to a set to identify the count of completed MPI ROs
#             #Same ronumber with different closeddate will be considered as two different ronumber
#             final_filtered_rows_df['unique_onumber'] = final_filtered_rows_df['ronumber'].astype(str)+'_'+final_filtered_rows_df['closeddate'].astype(str)
#             Completed_MPI_ROs = set(final_filtered_rows_df['unique_onumber'])

#             Completed_MPI_ROs_Perc = round_off(((len(Completed_MPI_ROs) / Opportunities) * 100),2)
#             print("Debug - calculate_mpi_penetration: len(Completed_MPI_ROs)",len(Completed_MPI_ROs))
#             print("Debug - calculate_mpi_penetration: Opportunities",Opportunities)

#         return Completed_MPI_ROs_Perc

#     except Exception as e:
#         print(f"Error calculating MPI penetration: {str(e)}")
#         return 0

# def calculate_menu_penetration(combined_revenue_details):
#     """Calculate menu penetration percentage"""
#     print("Debug - calculate_menu_penetration: Entering function",combined_revenue_details)
#     try:
#         # Filter for valid opportunities
#         menu_opportunities = combined_revenue_details[
#             (combined_revenue_details['group'].isin(['C', 'W'])) &
#             (combined_revenue_details['paytypegroup'].isin(['C', 'M', 'E', 'W', 'F'])) &
#             (combined_revenue_details['department'] != 'Body Shop')
#         ]
#         # print("Debug - calculate_menu_penetration: Menu opportunities",menu_opportunities)
#         if menu_opportunities.empty:
#             return 0
#         print("Debug - calculate_menu_penetration: Menu opportunities ############################--0>",menu_opportunities)
#         # Get menu setup data
#         menu_master_db = menuMasterTableResult()
#         menu_master_df = menu_master_db.getTableResult()
        
#         print("Debug - calculate_menu_penetration: Menu master DB ############################--1>",menu_master_db)
#         print("Debug - calculate_menu_penetration: Menu master DF ############################--2>",menu_master_df)
        
#         assigned_menu_opcodes_db = assignedMenuOpcodesTableResult()
#         assigned_menu_opcodes_df = assigned_menu_opcodes_db.getTableResult()
        
#         print("Debug - calculate_menu_penetration: Assigned menu opcodes DB ###########################---3>",assigned_menu_opcodes_db)
#         print("Debug - calculate_menu_penetration: Assigned menu opcodes DF ###########################---4>",assigned_menu_opcodes_df)
        
#         if menu_master_df.empty or assigned_menu_opcodes_df.empty:
#             return 0

#         # Get unique opportunity count
#         menu_opportunities['unique_ronumber'] = menu_opportunities['ronumber'].astype(str) + '_' + menu_opportunities['closeddate'].astype(str)
#         opportunities = menu_opportunities['unique_ronumber'].nunique()

#         print("Debug - calculate_menu_penetration: Opportunities ###############################---5>",opportunities)
#         if opportunities == 0:
#             return 0

#         # Get all menu opcodes
#         menu_opcodes = set(assigned_menu_opcodes_df['menu_opcode'].str.strip().str.upper())
#         print("Debug - calculate_menu_penetration: Menu opcodes ###############################---6>",menu_opcodes)
#         # Filter ROs with menu opcodes and customer pay conditions
#         menu_ros = menu_opportunities[
#             (menu_opportunities['lbropcode'].str.strip().str.upper().isin(menu_opcodes)) &
#             (menu_opportunities['group'] == 'C') &
#             (menu_opportunities['paytypegroup'].isin(['C', 'M', 'E']))
#         ]
#         print("Debug - calculate_menu_penetration: Menu ROS ###############################---7>",menu_ros)

#         # Get unique menu RO count
#         menu_sold = menu_ros['unique_ronumber'].nunique()

#         print("Debug - calculate_menu_penetration: Menu sold ###############################---8>",menu_sold)

#         # Calculate percentage
#         menu_penetration = round((menu_sold / opportunities) * 100, 2)
#         print("Debug - calculate_menu_penetration: Menu penetration ###############################---9>",menu_penetration)
#         # print(f"Debug - Menu Penetration: Opportunities={opportunities}, Menu Sold={menu_sold}, Percentage={menu_penetration}")
#         return menu_penetration

#     except Exception as e:
#         print(f"Error calculating menu penetration: {str(e)}")
#         print("Debug - Menu Data:")
#         print(f"Menu Master Records: {len(menu_master_df) if not menu_master_df.empty else 0}")
#         print(f"Menu Opcodes Records: {len(assigned_menu_opcodes_df) if not assigned_menu_opcodes_df.empty else 0}")
#         return 0


# def get_total_shopsupplies_details_combined(combined_revenue_details, advisor=None):
#     """
#     Calculate total shop supplies details combined matching the database function logic
    
#     Args:
#         combined_revenue_details: DataFrame with revenue details
#         advisor: List of service advisors or None for all
    
#     Returns:
#         dict: JSON data structure with shop supplies totals by month for different pay types
#     """
#     try:
#         # import pandas as pd
        
#         # Step 1: Filter base data similar to temp_data_all_revenue_details
#         base_filter = (
#             (combined_revenue_details['department'] == 'Service') &
#             (combined_revenue_details['hide_ro'] == '0')
#         )
        
#         # Apply advisor filter
#         if advisor is None or advisor == ['All']:
#             advisor_filter = True
#         else:
#             advisor_filter = combined_revenue_details['serviceadvisor'].isin(advisor)
        
#         temp_data = combined_revenue_details[base_filter & advisor_filter].copy()
        
#         # Step 2: Create temp_all_revenue_details equivalent
#         # First part: opcategory != 'N/A' and has some revenue/cost
#         valid_ops = temp_data[
#             (temp_data['opcategory'] != 'N/A') &
#             ~((temp_data['lbrsale'] == 0) & 
#               (temp_data['lbrsoldhours'] == 0) & 
#               (temp_data['prtextendedsale'] == 0) & 
#               (temp_data['prtextendedcost'] == 0)) &
#             (temp_data['paytypegroup'].isin(['C', 'E', 'M', 'W', 'F', 'I']))
#         ].copy()
        
#         # Map paytype using paytype_retail_flag_setting logic
#         valid_ops['mapped_paytype'] = valid_ops['paytypegroup'].map({
#             'C': 'C', 'E': 'E', 'M': 'M', 'W': 'W', 'F': 'F', 'I': 'I'
#         })
        
#         # Second part: opcategory == 'N/A' or zero revenue/cost (mapped to 'I')
#         invalid_ops = temp_data[
#             (temp_data['opcategory'] == 'N/A') |
#             ((temp_data['lbrsale'].fillna(0) == 0) & 
#              (temp_data['lbrsoldhours'].fillna(0) == 0) & 
#              (temp_data['prtextendedsale'].fillna(0) == 0) & 
#              (temp_data['prtextendedcost'].fillna(0) == 0))
#         ].copy()
#         invalid_ops['mapped_paytype'] = 'I'
        
#         # Combine both parts
#         temp_all_revenue = pd.concat([valid_ops, invalid_ops], ignore_index=True)
        
#         # Step 3: Determine RO_Type based on database logic
#         temp_all_revenue['RO_Type'] = ''
        
#         # Get ROs with Customer pay type
#         customer_ros = temp_all_revenue[temp_all_revenue['mapped_paytype'] == 'C']['ronumber'].unique()
#         temp_all_revenue.loc[temp_all_revenue['ronumber'].isin(customer_ros), 'RO_Type'] = 'C'
        
#         # Get ROs with Warranty pay type (only if not already marked as Customer)
#         warranty_ros = temp_all_revenue[
#             (temp_all_revenue['mapped_paytype'] == 'W') & 
#             (temp_all_revenue['RO_Type'] == '')
#         ]['ronumber'].unique()
#         temp_all_revenue.loc[temp_all_revenue['ronumber'].isin(warranty_ros), 'RO_Type'] = 'W'
        
#         # Mark remaining as Internal
#         temp_all_revenue.loc[temp_all_revenue['RO_Type'] == '', 'RO_Type'] = 'I'
        
#         # Step 4: Get unique ROs with their types
#         unique_ros = temp_all_revenue[['ronumber', 'closeddate', 'RO_Type', 'store_id']].drop_duplicates()
        
#         # Step 5: Get shop supplies data from total_details table
#         try:
#             # from db_handler.db_connector import totalDetailsTable
#             total_details_db_connect = totalDetailsTable()
#             total_details_df = total_details_db_connect.getTableResult()
#             print(f"Loaded {len(total_details_df)} records from total_details table")
#         except Exception as e:
#             print(f"Error loading total_details: {str(e)}")
#             print("Warning: totalshopsupply data not available. Using placeholder values.")
#             total_details_df = pd.DataFrame(columns=['ronumber', 'closeddate', 'store_id', 'totalshopsupply'])

#         # Join with shop supplies data
#         if not total_details_df.empty:
#             # Convert data types for proper joining
#             total_details_df['ronumber'] = total_details_df['ronumber'].astype(str)
#             total_details_df['store_id'] = total_details_df['store_id'].astype(str)
#             total_details_df['closeddate'] = pd.to_datetime(total_details_df['closeddate'])

#             # Ensure unique_ros has proper data types
#             unique_ros['ronumber'] = unique_ros['ronumber'].astype(str)
#             unique_ros['store_id'] = unique_ros['store_id'].astype(str)
#             unique_ros['closeddate'] = pd.to_datetime(unique_ros['closeddate'])

#             shop_supplies_data = pd.merge(
#                 total_details_df[['ronumber', 'closeddate', 'store_id', 'totalshopsupply']],
#                 unique_ros,
#                 on=['ronumber', 'closeddate', 'store_id'],
#                 how='inner'
#             )
#         else:
#             print("Warning: No shop supplies data available. Using placeholder values.")
#             shop_supplies_data = unique_ros.copy()
#             shop_supplies_data['totalshopsupply'] = 0
        
#         # Step 6: Convert closeddate to datetime and create month_year
#         shop_supplies_data['closeddate'] = pd.to_datetime(shop_supplies_data['closeddate'])
#         shop_supplies_data['month_year'] = shop_supplies_data['closeddate'].dt.strftime('%Y-%m')
#         shop_supplies_data['month_first_day'] = shop_supplies_data['closeddate'].dt.to_period('M').dt.start_time
        
#         # Step 7: Group by month and RO_Type, then sum shop supplies
#         monthly_grouped = shop_supplies_data.groupby(['month_first_day', 'RO_Type'])['totalshopsupply'].sum().reset_index()
        
#         # Step 8: Pivot to get separate columns for each RO_Type
#         monthly_pivot = monthly_grouped.pivot(index='month_first_day', columns='RO_Type', values='totalshopsupply').fillna(0)
        
#         # Ensure all required columns exist
#         for col in ['C', 'W', 'I']:
#             if col not in monthly_pivot.columns:
#                 monthly_pivot[col] = 0
        
#         # Calculate combined totals
#         monthly_pivot['Combined'] = monthly_pivot['C'] + monthly_pivot['W'] + monthly_pivot['I']
        
#         # Sort by date descending (most recent first)
#         monthly_pivot = monthly_pivot.sort_index(ascending=False)
        
#         # Step 9: Create JSON structure matching database output
#         json_data = [{
#             'datasets': [
#                 {
#                     'data': monthly_pivot['Combined'].astype(str).tolist(),
#                     'label': 'Combined',
#                     'chartId': '1359'
#                 },
#                 {
#                     'data': monthly_pivot['C'].astype(str).tolist(),
#                     'label': 'Customer Pay',
#                     'chartId': '1360'
#                 },
#                 {
#                     'data': monthly_pivot['W'].astype(str).tolist(),
#                     'label': 'Warranty',
#                     'chartId': '1361'
#                 },
#                 {
#                     'data': monthly_pivot['I'].astype(str).tolist(),
#                     'label': 'Internal',
#                     'chartId': '1362'
#                 }
#             ],
#             'labels': monthly_pivot.index.strftime('%Y-%m-%d').tolist()
#         }]
        
#         return {'json_data': json_data}
        
#     except Exception as e:
#         print(f"Error calculating shop supplies details: {str(e)}")
#         return {
#             'json_data': [{
#                 'datasets': [
#                     {
#                         'data': ['0.00'],
#                         'label': 'Combined',
#                         'chartId': '1359'
#                     },
#                     {
#                         'data': ['0.00'],
#                         'label': 'Customer Pay',
#                         'chartId': '1360'
#                     },
#                     {
#                         'data': ['0.00'],
#                         'label': 'Warranty',
#                         'chartId': '1361'
#                     },
#                     {
#                         'data': ['0.00'],
#                         'label': 'Internal',
#                         'chartId': '1362'
#                     }
#                 ],
#                 'labels': ['No Data']
#             }]
#         }


# def get_shop_supplies_summary(combined_revenue_details, advisor=None, target_month=None):
#     """
#     Get shop supplies summary for a specific month or latest month
    
#     Args:
#         combined_revenue_details: DataFrame with revenue details
#         advisor: List of service advisors or None for all
#         target_month: Specific month in 'YYYY-MM' format, or None for latest
    
#     Returns:
#         dict: Shop supplies totals by pay type
#     """
#     try:
#         # Get the full monthly data
#         monthly_data = get_total_shopsupplies_details_combined(combined_revenue_details, advisor)
        
#         # Extract the data
#         datasets = monthly_data['json_data'][0]['datasets']
#         labels = monthly_data['json_data'][0]['labels']
        
#         if not labels or labels[0] == 'No Data':
#             return {
#                 'combined': 0.00,
#                 'customer_pay': 0.00,
#                 'warranty': 0.00,
#                 'internal': 0.00,
#                 'month': 'No Data'
#             }
        
#         # Find target month index (default to most recent - index 0)
#         index = 0
#         if target_month:
#             target_date = f"{target_month}-01"
#             if target_date in labels:
#                 index = labels.index(target_date)
        
#         return {
#             'combined': float(datasets[0]['data'][index]),
#             'customer_pay': float(datasets[1]['data'][index]),
#             'warranty': float(datasets[2]['data'][index]),
#             'internal': float(datasets[3]['data'][index]),
#             'month': labels[index]
#         }
        
#     except Exception as e:
#         print(f"Error getting shop supplies summary: {str(e)}")
#         return {
#             'combined': 0.00,
#             'customer_pay': 0.00,
#             'warranty': 0.00,
#             'internal': 0.00,
#             'month': 'Error'
#         }

# def initialize_empty_metrics():
#     """Initialize empty metrics structure"""
#     return {
#         "one_line_metrics": {
#             "under_60k": 0, "over_60k": 0, "total_shop": 0,
#             "perc_under_60k": 0, "perc_over_60k": 0, "perc_total_shop": 0
#         },
#         "multi_line_metrics": {
#             "under_60k": 0, "over_60k": 0, "total_shop": 0,
#             "perc_under_60k": 0, "perc_over_60k": 0, "perc_total_shop": 0
#         },
#         "average_open_days": {
#             "customer_pay": 0, "warranty": 0, "internal": 0,
#             "extended_service": 0, "maintenance": 0, "factory_service": 0
#         },
#         "parts_to_labor_ratio": {
#             "overall": 0, "competitive": 0, "maintenance": 0, "repair": 0
#         },
#         "labor_hours_percentage": {
#             "customer_pay": 0, "warranty": 0, "internal": 0,
#             "extended_service": 0, "maintenance": 0, "factory_service": 0
#         },
#         "mpi_penetration_percentage": 0,
#         "menu_penetration_percentage": 0
#     }

# def db_execution_special_metrics(target_date_str, advisor, tech, retail_flag, columns_to_check):
#     """Handle database operations and execute special metrics processing"""
    
#     try:
#         # Get target month date range
#         month_start, month_end = get_month_date_range_from_target(target_date_str)
        
#         # Fetch all data from database
#         all_revenue_details_table_db_connect = allRevenueDetailsTable()
#         all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()
#         all_revenue_details_df.to_csv('all_revenue_details.csv', index=False)
        
#         if all_revenue_details_df.empty:
#             return None
        
#         # Convert date column and other preprocessing
#         columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
#         all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
#         all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))
        
#         # Define customer and warranty pay types based on retail_flag
#         if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
#             customer_pay_types = {'C'}
#             warranty_pay_types = {'W', 'F', 'M', 'E'}
#         elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
#             customer_pay_types = {'C', 'M'}
#             warranty_pay_types = {'W', 'F', 'E'}
#         elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
#             customer_pay_types = {'C', 'E'}
#             warranty_pay_types = {'W', 'F', 'M'}
#         elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
#             customer_pay_types = {'C', 'E', 'M'}
#             warranty_pay_types = {'W', 'F'}
        
#         target_month_result = process_target_month_special_metrics(
#             all_revenue_details_df, 
#             month_start, 
#             month_end,            
#             advisor, 
#             tech, 
#             retail_flag, 
#             customer_pay_types, 
#             warranty_pay_types, 
#             columns_to_check
#         )
        
#         return target_month_result, customer_pay_types, warranty_pay_types
        
#     except Exception as e:
#         print(f"ERROR in db_execution_special_metrics: {str(e)}")
#         return None, None, None

# def db_calculation_special_metrics():
#     """Main execution function for special metrics calculation"""

#     # Configuration variables
#     columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
#     retail_flag = {'C','M','E'}

#     # Get configuration from global config object
#     advisor_set = config.advisor if hasattr(config, 'advisor') else 'all'
#     tech_set = config.technician if hasattr(config, 'technician') else 'all'
#     TARGET_MONTHS_YEARS = config.target_month_year
    
#     # Process advisor configuration
#     if isinstance(advisor_set, str):
#         if advisor_set.lower() == 'all':
#             advisor = {'all'}
#         elif ',' in advisor_set:
#             advisor = {x.strip() for x in advisor_set.split(',')}
#         else:
#             advisor = {advisor_set.strip()}
#     else:
#         advisor = {'all'}
    
#     # Process technician configuration
#     if isinstance(tech_set, str):
#         if tech_set.lower() == 'all':
#             tech = {'all'}
#         elif ',' in tech_set:
#             tech = {x.strip() for x in tech_set.split(',')}
#         else:
#             tech = {tech_set.strip()}
#     else:
#         tech = {'all'}
    
#     # Execute database operations and processing
#     target_date_str = TARGET_MONTHS_YEARS[0]
#     target_month_result, customer_pay_types, warranty_pay_types = db_execution_special_metrics(
#         target_date_str, advisor, tech, retail_flag, columns_to_check
#     )
#     # Process results
#     if target_month_result:
#         final_result_set = {
#             "analysis_info": {
#                 "target_month": target_date_str,
#                 "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
#                 "advisor_filter": list(advisor),
#                 "technician_filter": list(tech),
#                 "customer_pay_types": list(customer_pay_types),
#                 "warranty_pay_types": list(warranty_pay_types)
#             },
#             "target_month_results": target_month_result
#         }
        
#         # Write results to JSON file
#         output_filename = "chart_processing_results/special_metrics_calculated_value.json"
#         with open(output_filename, 'w', encoding='utf-8') as json_file:
#             json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)
        
#         print(f"\nSpecial metrics data written successfully to {output_filename}")
        
#         # Display summary
#         print(f"\nSpecial Metrics Summary for {target_month_result['target_month_name']}:")
#         print(f"  Total ROs: {target_month_result['total_ros']}")
#         print(f"  One-Line ROs: {target_month_result['special_metrics']['one_line_metrics']['total_shop']}")
#         print(f"  Multi-Line ROs: {target_month_result['special_metrics']['multi_line_metrics']['total_shop']}")
#         print(f"  Parts to Labor Ratio: {target_month_result['special_metrics']['parts_to_labor_ratio']['overall']}")
        
#     else:
#         print(f"No data available for target month {target_date_str}")

# def parse_arguments():
#     """Parse command line arguments"""
#     parser = argparse.ArgumentParser(description="Run special metrics validation with store context.")

#     parser.add_argument("--store_id", required=True, help="Store ID")
#     parser.add_argument("--store_name", required=True, help="Store Name")
#     parser.add_argument("--start_date", required=True, help="Start date (YYYY-MM-DD)")
#     parser.add_argument("--end_date", required=True, help="End date (YYYY-MM-DD)")
#     parser.add_argument("--fopc_month", required=True, help="Current FOPC month being evaluated (format: YYYY-MM)")
#     parser.add_argument("--pre_fopc_month", required=True, help="Previous FOPC month for comparison (format: YYYY-MM)")
#     parser.add_argument("--database_name", required=True, help="Name of the database to connect (e.g., sampackag)")
#     parser.add_argument("--working_days", required=True, help="Number of working days in the selected date range (e.g., 46.74)")
#     parser.add_argument("--advisor", required=True, help="Advisor name or 'all' to include all advisors")
#     parser.add_argument("--technician", required=True, help="Technician name or 'all' to include all technicians")
#     parser.add_argument("--site_url", required=True, help="Base URL of the site (e.g., https://sampackag.fixedops.cc/)")
#     parser.add_argument("--last_month", required=True, help="Last month for client report card three months (format: YYYY-MM)")
#     parser.add_argument("--role", required=True, help="Name of role")
#     parser.add_argument("--target_month_year", required=True, help="Target month/year, e.g. 2023-11-01")

#     return parser.parse_args()

# def set_config_from_args(args):
#     """Set global config from command line arguments"""
#     config.store_id = args.store_id
#     config.store_name = args.store_name
#     config.start_date = args.start_date
#     config.end_date = args.end_date
#     config.fopc_month = args.fopc_month
#     config.pre_fopc_month = args.pre_fopc_month
#     config.database_name = args.database_name
#     config.working_days = float(args.working_days)
#     config.advisor = args.advisor
#     config.technician = args.technician
#     config.site_url = args.site_url
#     config.last_month = args.last_month
#     config.role = args.role

#     # Convert target_month_year from YYYY-MM-DD to YYYY-MM format
#     target_date = args.target_month_year
#     if len(target_date) == 10:  # YYYY-MM-DD format
#         target_date = target_date[:7]  # Convert to YYYY-MM
#     config.target_month_year = [target_date]

# def run_validation():
#     """Run validation function - called by the main test runner"""
#     try:
#         db_calculation_special_metrics()
#     except Exception as e:
#         print(f"Error in special metrics validation: {str(e)}")
#         raise

# # Example usage
# if __name__ == "__main__":
#     try:
#         # Parse command line arguments
#         args = parse_arguments()
#         set_config_from_args(args)
#         db_calculation_special_metrics()
#     except SystemExit:
#         # If no arguments provided, use default configuration for testing
#         print("No arguments provided, using default configuration for testing...")
#         config.store_id = "244284397"
#         config.store_name = "Carriage Kia of Woodstock"
#         config.start_date = "2025-04-01"
#         config.end_date = "2025-07-30"
#         config.fopc_month = "2025-04"
#         config.pre_fopc_month = "2025-01"
#         config.database_name = "fopc_simt_prime"
#         config.working_days = 73.8
#         config.advisor = "all"
#         config.technician = "all"
#         config.site_url = "https://carriageag-simt.fixedopspc.com"
#         config.last_month = "2025-07"
#         config.role = "Admin"
#         config.target_month_year = ["2025-07"]

#         db_calculation_special_metrics()

import math
import sys
import os
import argparse

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import pandas as pd
import numpy as np
from decimal import Decimal, ROUND_HALF_UP
import json

# Import your existing database connectors
from lib.pattern.sampackag.db_handler.db_connector import (
    getCustomerPayTypeGroupsList, menuMasterTableResult,
    menuServiceTypeTableResult, assignedMenuModelsTableResult,
    assignedMenuOpcodesTableResult, MPISetupTableResult,
    MPIOpcodesTableResult, allRevenueDetailsCPOverview, allRevenueDetailsTable,totalDetailsTable
)

# Import config for accessing global configuration
from lib.pattern.config import config

def round_off(n, decimals=0):
    """Round off numbers with proper decimal handling"""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    """Check if all specified columns sum to zero"""
    total_sum = df[columns].sum().sum()
    return total_sum == 0

def get_month_date_range_from_target(target_date_str):
    """Get the start and end date for the target month"""
    # Modified to handle YYYY-MM format
    target_date = datetime.strptime(target_date_str, "%Y-%m")    
    month_start = target_date.replace(day=1)    
    month_end = month_start + relativedelta(months=1) - timedelta(days=1)    
    return month_start, month_end

def calculate_shop_supplies_details(combined_revenue_details, advisor=None):
    """
    Calculate shop supplies details matching the database function logic

    Args:
        combined_revenue_details: DataFrame with revenue details
        advisor: List of service advisors or None for all

    Returns:
        dict: Shop supplies totals by month for different pay types
    """
    try:
        # Step 1: Filter base data similar to temp_data_all_revenue_details
        base_filter = (
            (combined_revenue_details['department'] == 'Service') &
            (combined_revenue_details['hide_ro'] == '0')
        )

        # Apply advisor filter
        if advisor is None or advisor == {'all'}:
            advisor_filter = True
        else:
            advisor_filter = combined_revenue_details['serviceadvisor'].isin(advisor)

        temp_data = combined_revenue_details[base_filter & advisor_filter].copy()

        if temp_data.empty:
            return initialize_empty_shop_supplies()
        
        # Step 2: Create temp_all_revenue_details equivalent
        # First part: opcategory != 'N/A' and has some revenue/cost
        valid_ops = temp_data[
            (temp_data['opcategory'] != 'N/A') &
            ~((temp_data['lbrsale'].fillna(0) == 0) & 
              (temp_data['lbrsoldhours'].fillna(0) == 0) & 
              (temp_data['prtextendedsale'].fillna(0) == 0) & 
              (temp_data['prtextendedcost'].fillna(0) == 0)) &
            (temp_data['paytypegroup'].isin(['C', 'E', 'M', 'W', 'F', 'I']))
        ].copy()
        
        # Map paytype using paytype_retail_flag_setting logic
        paytype_mapping = {'C': 'C', 'E': 'E', 'M': 'M', 'W': 'W', 'F': 'F', 'I': 'I'}
        valid_ops['mapped_paytype'] = valid_ops['paytypegroup'].map(paytype_mapping)
        
        # Second part: opcategory == 'N/A' or zero revenue/cost (mapped to 'I')
        invalid_ops = temp_data[
            (temp_data['opcategory'] == 'N/A') |
            ((temp_data['lbrsale'].fillna(0) == 0) & 
             (temp_data['lbrsoldhours'].fillna(0) == 0) & 
             (temp_data['prtextendedsale'].fillna(0) == 0) & 
             (temp_data['prtextendedcost'].fillna(0) == 0))
        ].copy()
        invalid_ops['mapped_paytype'] = 'I'
        
        # Combine both parts
        temp_all_revenue = pd.concat([valid_ops, invalid_ops], ignore_index=True)
        
        # Step 3: Determine RO_Type based on database logic
        temp_all_revenue['RO_Type'] = ''
        
        # Get ROs with Customer pay type
        customer_ros = temp_all_revenue[temp_all_revenue['mapped_paytype'] == 'C']['ronumber'].unique()
        temp_all_revenue.loc[temp_all_revenue['ronumber'].isin(customer_ros), 'RO_Type'] = 'C'
        
        # Get ROs with Warranty pay type (only if not already marked as Customer)
        warranty_ros = temp_all_revenue[
            (temp_all_revenue['mapped_paytype'] == 'W') & 
            (temp_all_revenue['RO_Type'] == '')
        ]['ronumber'].unique()
        temp_all_revenue.loc[temp_all_revenue['ronumber'].isin(warranty_ros), 'RO_Type'] = 'W'
        
        # Mark remaining as Internal
        temp_all_revenue.loc[temp_all_revenue['RO_Type'] == '', 'RO_Type'] = 'I'
        
        # Step 4: Get unique ROs with their types
        unique_ros = temp_all_revenue[['ronumber', 'closeddate', 'RO_Type', 'store_id']].drop_duplicates()
        
        # Step 5: Get shop supplies data from total_details table
        try:
            total_details_db_connect = totalDetailsTable()
            total_details_df = total_details_db_connect.getTableResult()
            print(f"Loaded {len(total_details_df)} records from total_details table")
        except Exception as e:
            print(f"Error loading total_details: {str(e)}")
            print("Warning: totalshopsupply data not available. Using placeholder values.")
            total_details_df = pd.DataFrame(columns=['ronumber', 'closeddate', 'store_id', 'totalshopsupply'])

        # Join with shop supplies data
        if not total_details_df.empty:
            # Convert data types for proper joining
            total_details_df['ronumber'] = total_details_df['ronumber'].astype(str)
            total_details_df['store_id'] = total_details_df['store_id'].astype(str)
            total_details_df['closeddate'] = pd.to_datetime(total_details_df['closeddate'])

            # Ensure unique_ros has proper data types
            unique_ros['ronumber'] = unique_ros['ronumber'].astype(str)
            unique_ros['store_id'] = unique_ros['store_id'].astype(str)
            unique_ros['closeddate'] = pd.to_datetime(unique_ros['closeddate'])

            shop_supplies_data = pd.merge(
                total_details_df[['ronumber', 'closeddate', 'store_id', 'totalshopsupply']],
                unique_ros,
                on=['ronumber', 'closeddate', 'store_id'],
                how='inner'
            )
        else:
            print("Warning: No shop supplies data available. Using placeholder values.")
            shop_supplies_data = unique_ros.copy()
            shop_supplies_data['totalshopsupply'] = 0
        
        if shop_supplies_data.empty:
            return initialize_empty_shop_supplies()
        
        # Step 6: Convert closeddate to datetime and create month_year
        shop_supplies_data['closeddate'] = pd.to_datetime(shop_supplies_data['closeddate'])
        shop_supplies_data['month_year'] = shop_supplies_data['closeddate'].dt.strftime('%Y-%m')
        shop_supplies_data['month_first_day'] = shop_supplies_data['closeddate'].dt.to_period('M').dt.start_time
        
        # Step 7: Group by month and RO_Type, then sum shop supplies
        monthly_grouped = shop_supplies_data.groupby(['month_first_day', 'RO_Type'])['totalshopsupply'].sum().reset_index()
        
        if monthly_grouped.empty:
            return initialize_empty_shop_supplies()
        
        # Step 8: Pivot to get separate columns for each RO_Type
        monthly_pivot = monthly_grouped.pivot(index='month_first_day', columns='RO_Type', values='totalshopsupply').fillna(0)
        
        # Ensure all required columns exist
        for col in ['C', 'W', 'I']:
            if col not in monthly_pivot.columns:
                monthly_pivot[col] = 0
        
        # Calculate combined totals
        monthly_pivot['Combined'] = monthly_pivot['C'] + monthly_pivot['W'] + monthly_pivot['I']
        
        # Sort by date descending (most recent first)
        monthly_pivot = monthly_pivot.sort_index(ascending=False)
        
        # Return the most recent month's data
        if not monthly_pivot.empty:
            latest_month = monthly_pivot.iloc[0]
            return {
                "combined": round_off(latest_month['Combined'], 2),
                "customer_pay": round_off(latest_month['C'], 2),
                "warranty": round_off(latest_month['W'], 2),
                "internal": round_off(latest_month['I'], 2),
                "month": monthly_pivot.index[0].strftime('%Y-%m')
            }
        
        return initialize_empty_shop_supplies()
        
    except Exception as e:
        print(f"Error calculating shop supplies details: {str(e)}")
        return initialize_empty_shop_supplies()

def initialize_empty_shop_supplies():
    """Initialize empty shop supplies structure"""
    return {
        "combined": 0.00,
        "customer_pay": 0.00,
        "warranty": 0.00,
        "internal": 0.00,
        "month": "No Data"
    }

def process_target_month_special_metrics(all_revenue_details_df, month_start, month_end, advisor, tech,
                                       retail_flag, customer_pay_types, warranty_pay_types, columns_to_check):
    """Process special metrics data for the target month and return results"""

    # Convert to pandas datetime for consistent comparison
    month_start_pd = pd.to_datetime(month_start)
    month_end_pd = pd.to_datetime(month_end)
    print(f"Target month range: {month_start_pd.date()} to {month_end_pd.date()}")

    # Ensure closeddate column is in datetime format
    if 'closeddate' in all_revenue_details_df.columns:
        print(f"Debug - Input DataFrame shape: {all_revenue_details_df.shape}")
        print(f"Debug - closeddate dtype in function: {all_revenue_details_df['closeddate'].dtype}")
        print(f"Debug - Date range for filtering: {month_start_pd} to {month_end_pd}")
        print(f"Debug - Sample dates in data: {all_revenue_details_df['closeddate'].head()}")

        # Check if dates are already datetime
        if all_revenue_details_df['closeddate'].dtype != 'datetime64[ns]':
            all_revenue_details_df['closeddate'] = pd.to_datetime(all_revenue_details_df['closeddate'])

    # Filter data for the specific target month
    month_data = all_revenue_details_df[
        (all_revenue_details_df['closeddate'] >= month_start_pd) &
        (all_revenue_details_df['closeddate'] <= month_end_pd)
    ]

    print(f"Debug - Filtered month data shape: {month_data.shape}")

    if month_data.empty:
        print(f"No data available for target month {month_start_pd.date()}")
        return None
    
    # Apply existing filtering logic
    filtered_df = month_data[
        (month_data['department'] == 'Service') & 
        (month_data['hide_ro'] != True)
    ]
    
    if filtered_df.empty:
        return None
    
    filtered_df = filtered_df.copy()
    filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)
    
    # Initialize the combined revenue details with group assignment
    combined_revenue_details = filtered_df.copy()
    combined_revenue_details['group'] = pd.Series(dtype="string")
    
    # Group assignment logic (same as original)
    temp_revenue_details = combined_revenue_details.copy()
    temp_revenue_details.loc[temp_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0

    for ro_number in combined_revenue_details['unique_ro_number'].unique():
        ro_specific_rows = temp_revenue_details[temp_revenue_details['unique_ro_number'] == ro_number]        
        ro_specific_rows_C = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(customer_pay_types)]
        ro_specific_rows_W = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(warranty_pay_types)]       
        zero_sales_C = zero_sales_check(ro_specific_rows_C, columns_to_check)
        zero_sales_W = zero_sales_check(ro_specific_rows_W, columns_to_check)        
        
        if not ro_specific_rows_C.empty and not zero_sales_C:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'C'
        elif not ro_specific_rows_W.empty and not zero_sales_W:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'W'
        else:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'I'
    
    # Apply filters based on advisor and tech conditions
    if advisor == {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details['unique_ro_number'].unique()
    elif advisor != {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
    elif advisor == {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
    elif advisor != {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[(combined_revenue_details['serviceadvisor'].astype(str).isin(advisor)) & 
            (combined_revenue_details['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()
    
    # Apply the advisor and tech filter conditions
    combined_revenue_details = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(matching_ro_numbers)]
    combined_revenue_details = combined_revenue_details.reset_index(drop=True)
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    
    # Calculate RO counts
    Scorecard_10_CP = combined_revenue_details.loc[combined_revenue_details['group'] == 'C', 'unique_ro_number'].nunique()
    Scorecard_10_Wty = combined_revenue_details.loc[combined_revenue_details['group'] == 'W', 'unique_ro_number'].nunique()
    Scorecard_10_Int = combined_revenue_details.loc[combined_revenue_details['group'] == 'I', 'unique_ro_number'].nunique()
    
    # Initialize default values
    special_metrics_results = {
        "one_line_metrics": {
            "under_60k": 0,
            "over_60k": 0,
            "total_shop": 0,
            "perc_under_60k": 0,
            "perc_over_60k": 0,
            "perc_total_shop": 0
        },
        "multi_line_metrics": {
            "under_60k": 0,
            "over_60k": 0,
            "total_shop": 0,
            "perc_under_60k": 0,
            "perc_over_60k": 0,
            "perc_total_shop": 0
        },
        "average_open_days": {
            "customer_pay": 0,
            "warranty": 0,
            "internal": 0,
            "extended_service": 0,
            "maintenance": 0,
            "factory_service": 0
        },
        "parts_to_labor_ratio": {
            "overall": 0,
            "competitive": 0,
            "maintenance": 0,
            "repair": 0
        },
        "labor_hours_percentage": {
            "customer_pay": 0,
            "warranty": 0,
            "internal": 0,
            "extended_service": 0,
            "maintenance": 0,
            "factory_service": 0
        },
        "mpi_penetration_percentage": 0,
        "menu_penetration_percentage": 0,
        "shop_supplies": initialize_empty_shop_supplies()
    }
    
    # Only proceed with calculations if we have data
    if (Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int) > 0:
        special_metrics_results = calculate_special_metrics(
            combined_revenue_details, customer_pay_types, warranty_pay_types, 
            columns_to_check, Scorecard_10_CP
        )
    
    # Calculate shop supplies regardless of customer pay data
    shop_supplies_results = calculate_shop_supplies_details(combined_revenue_details, advisor)
    special_metrics_results["shop_supplies"] = shop_supplies_results
    
    return {
        "target_month": month_start.strftime('%Y-%m'),
        "target_month_name": month_start.strftime('%B %Y'),
        "total_ros": Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int,
        "ro_counts": {
            "customer_pay_ros": Scorecard_10_CP,
            "warranty_ros": Scorecard_10_Wty,
            "internal_ros": Scorecard_10_Int
        },
        "special_metrics": special_metrics_results
    }

def calculate_special_metrics(combined_revenue_details, customer_pay_types, warranty_pay_types, columns_to_check, total_cp_ros):
    """Calculate all special metrics"""
    
    # Filter for customer pay data with mileage information
    all_revenue_details_list = combined_revenue_details.to_dict('records')
    
    total_revenue_details_C = [
        row for row in all_revenue_details_list
        if (row['group'] == 'C') and (row['paytypegroup'] in customer_pay_types) and 
        (row['mileage'] is not None)
    ]
    
    if not total_revenue_details_C:
        return initialize_empty_metrics()
    
    total_revenue_details_C_df = pd.DataFrame(total_revenue_details_C)
    
    # Remove zero-sales rows
    total_revenue_details_C_df = total_revenue_details_C_df[
        ~((total_revenue_details_C_df['lbrsale'].fillna(0) == 0) &
          (total_revenue_details_C_df['lbrsoldhours'].fillna(0) == 0) &
          (total_revenue_details_C_df['prtextendedsale'].fillna(0) == 0) &
          (total_revenue_details_C_df['prtextendedcost'].fillna(0) == 0))
    ]
    
    # Convert numeric columns
    numeric_cols = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    for col in numeric_cols:
        total_revenue_details_C_df[col] = pd.to_numeric(total_revenue_details_C_df[col], errors='coerce').fillna(0)
    
    # Re-filter after numeric conversion to remove rows with all zero sales
    total_revenue_details_C_df = total_revenue_details_C_df[
        ~((total_revenue_details_C_df['lbrsale'].fillna(0) == 0) &
          (total_revenue_details_C_df['lbrsoldhours'].fillna(0) == 0) &
          (total_revenue_details_C_df['prtextendedsale'].fillna(0) == 0) &
          (total_revenue_details_C_df['prtextendedcost'].fillna(0) == 0))
    ]
    
    if total_revenue_details_C_df.empty:
        return initialize_empty_metrics()
    
    # Calculate one-line and multi-line metrics using the original logic
    one_line_metrics, multi_line_metrics = calculate_line_ro_metrics_original(total_revenue_details_C_df, total_cp_ros)
    
    # Calculate average open days
    avg_open_days = calculate_average_open_days(combined_revenue_details)
    
    # Calculate comprehensive special metrics (labor hours, parts to labor ratio, open days)
    comprehensive_metrics = calculate_comprehensive_special_metrics(combined_revenue_details)

    # Extract individual metrics for backward compatibility
    parts_to_labor = comprehensive_metrics["parts_to_labor_ratio"]
    labor_hours_perc = comprehensive_metrics["labor_hours_percentage"]
    average_open_days = comprehensive_metrics["average_open_days"]
    ro_counts = comprehensive_metrics["ro_counts"]
    
    # Calculate MPI and Menu penetration (simplified versions)
    mpi_penetration = calculate_mpi_penetration(combined_revenue_details)
    menu_penetration = calculate_menu_penetration(combined_revenue_details)
    
    return {
        "one_line_metrics": one_line_metrics,
        "multi_line_metrics": multi_line_metrics,
        "average_open_days": avg_open_days,
        "parts_to_labor_ratio": parts_to_labor,
        "labor_hours_percentage": labor_hours_perc,
        "mpi_penetration_percentage": mpi_penetration,
        "menu_penetration_percentage": menu_penetration,
        "shop_supplies": initialize_empty_shop_supplies()  # Will be calculated separately
    }

def calculate_line_ro_metrics_original(df, total_cp_ros):
    """Calculate one-line and multi-line RO metrics using original logic"""
    
    # Split by mileage first
    below_60k_df = df[df['mileage'].astype(int) < 60000]
    above_60k_df = df[df['mileage'].astype(int) >= 60000]
    
    total_ro_count_below_60k = below_60k_df['unique_ro_number'].nunique()
    total_ro_count_above_60k = above_60k_df['unique_ro_number'].nunique()
    
    # Based on the jobs on each RO, splitting the Customer Pay ROs into different list, One Line RO and Multi Line RO
    value_counts = df['unique_ro_number'].value_counts()
    one_line_ROs = value_counts[value_counts == 1].index
    One_Line_RO_Details = df[df['unique_ro_number'].isin(one_line_ROs)]
    Multi_Line_RO_Details = df[~df['unique_ro_number'].isin(one_line_ROs)]
    
    # Handle menu impact on One Line RO Details (this is the critical part from original code)
    One_Line_RO_Details = handle_menu_impact_on_one_line(One_Line_RO_Details, Multi_Line_RO_Details)
    
    # Based on mileage, splitting the One Line and Multi line list into two, Below and Above 60k
    One_Line_RO_Details_below60k = One_Line_RO_Details[One_Line_RO_Details['mileage'].astype(int) < 60000]
    One_Line_RO_Details_above60k = One_Line_RO_Details[One_Line_RO_Details['mileage'].astype(int) >= 60000]
    Multi_Line_RO_Details_below60k = Multi_Line_RO_Details[Multi_Line_RO_Details['mileage'].astype(int) < 60000]
    Multi_Line_RO_Details_above60k = Multi_Line_RO_Details[Multi_Line_RO_Details['mileage'].astype(int) >= 60000]
    
    # One Line RO count calculation
    one_line_count_below_60k = One_Line_RO_Details_below60k.shape[0]
    one_line_count_above_60k = One_Line_RO_Details_above60k.shape[0]
    
    # Multi-line RO count calculation
    multi_line_count_below_60k = Multi_Line_RO_Details_below60k['unique_ro_number'].nunique()
    multi_line_count_above_60k = Multi_Line_RO_Details_above60k['unique_ro_number'].nunique()
    
    one_line_total = one_line_count_below_60k + one_line_count_above_60k
    multi_line_total = multi_line_count_below_60k + multi_line_count_above_60k
    
    # Calculate percentages
    one_line_perc_below_60k = round_off((one_line_count_below_60k / total_ro_count_below_60k) * 100, 2) if total_ro_count_below_60k > 0 else 0
    one_line_perc_above_60k = round_off((one_line_count_above_60k / total_ro_count_above_60k) * 100, 2) if total_ro_count_above_60k > 0 else 0
    one_line_perc_total = round_off((one_line_total / total_cp_ros) * 100, 2) if total_cp_ros > 0 else 0
    
    multi_line_perc_below_60k = round_off((multi_line_count_below_60k / total_ro_count_below_60k) * 100, 2) if total_ro_count_below_60k > 0 else 0
    multi_line_perc_above_60k = round_off((multi_line_count_above_60k / total_ro_count_above_60k) * 100, 2) if total_ro_count_above_60k > 0 else 0
    multi_line_perc_total = round_off((multi_line_total / total_cp_ros) * 100, 2) if total_cp_ros > 0 else 0
    
    one_line_metrics = {
        "under_60k": one_line_count_below_60k,
        "over_60k": one_line_count_above_60k,
        "total_shop": one_line_total,
        "perc_under_60k": one_line_perc_below_60k,
        "perc_over_60k": one_line_perc_above_60k,
        "perc_total_shop": one_line_perc_total
    }
    
    multi_line_metrics = {
        "under_60k": multi_line_count_below_60k,
        "over_60k": multi_line_count_above_60k,
        "total_shop": multi_line_total,
        "perc_under_60k": multi_line_perc_below_60k,
        "perc_over_60k": multi_line_perc_above_60k,
        "perc_total_shop": multi_line_perc_total
    }
    
    return one_line_metrics, multi_line_metrics

def handle_menu_impact_on_one_line(One_Line_RO_Details, Multi_Line_RO_Details):
    """Handle menu impact on one-line ROs as per original logic"""
    
    try:
        # Get menu setup data
        menu_master_db = menuMasterTableResult()
        menu_master_df = menu_master_db.getTableResult()
        
        menu_service_type_db = menuServiceTypeTableResult()
        menu_service_type_df = menu_service_type_db.getTableResult()
        
        assigned_menu_models_db = assignedMenuModelsTableResult()
        assigned_menu_models_df = assigned_menu_models_db.getTableResult()
        
        assigned_menu_opcodes_db = assignedMenuOpcodesTableResult()
        assigned_menu_opcodes_df = assigned_menu_opcodes_db.getTableResult()
        
        if menu_master_df.empty:
            return One_Line_RO_Details
        
        # Identifying the available menus from menu master table
        menu_names = set(menu_master_df['menu_name'])
        
        # storing the list of available default menu
        default_menu_series = menu_master_df[menu_master_df['is_default'].astype(int) == 1]['menu_name']
        
        # Identifying the default menu name
        if not default_menu_series.empty:
            default_menu = default_menu_series.iloc[0]
        else:
            default_menu = np.nan
        
        # Create a copy to avoid modifying original
        One_Line_RO_Details_copy = One_Line_RO_Details.copy()
        
        # Checking whether models are assigned to the available menu
        if not assigned_menu_models_df.empty:
            # Create a mapping from 'model' to 'menu_name'
            model_to_menu_map = assigned_menu_models_df.set_index('model')['menu_name'].to_dict()
            # Map the 'model' column to the 'mapped_menu'
            One_Line_RO_Details_copy['mapped_menu'] = One_Line_RO_Details_copy['model'].map(model_to_menu_map).fillna(default_menu)
        else:
            # if no model mapping available all models mapped to default menu
            One_Line_RO_Details_copy['mapped_menu'] = default_menu
        
        # if no menus are added, all values under menu sales will be 0
        if menu_names:
            for name in menu_names:
                One_Line_RO_Details_copy[name] = ''
            
            # Create a dictionary to map service_type_id to service_type using menu_service_type table
            service_type_mapping = menu_service_type_df.set_index('id')['service_type'].to_dict()
            
            # Create a set of menu names from the menu_master table only if it is available on the columns
            menu_names_available = [name for name in menu_names if name in One_Line_RO_Details_copy.columns]
            
            # Iterate over each row in One_Line_RO_Details_copy
            for i, row in One_Line_RO_Details_copy.iterrows():
                mileage = row['mileage']
                
                # Initialize menu_names columns to NaN
                One_Line_RO_Details_copy.loc[i, menu_names_available] = np.nan
                
                # Find rows in menu_master_df where mileage is within the range
                # Convert mileage to numeric and range values to numeric for proper comparison
                mileage_numeric = pd.to_numeric(mileage, errors='coerce') or 0
                matching_menus = menu_master_df[
                    (pd.to_numeric(menu_master_df['range_from'], errors='coerce').fillna(0) <= mileage_numeric) &
                    (pd.to_numeric(menu_master_df['range_to'], errors='coerce').fillna(float('inf')) >= mileage_numeric)
                ]
                
                for _, menu_row in matching_menus.iterrows():
                    menu_name = menu_row['menu_name']
                    service_type_id = menu_row['service_type_id']
                    item_count = menu_row['items']
                    
                    # Check if the menu_name column exists in One_Line_RO_Details_copy
                    if menu_name in One_Line_RO_Details_copy.columns:
                        # Map service_type_id to service_type
                        service_type = service_type_mapping.get(service_type_id, None)
                        
                        # Update the value of the column with service_type
                        if service_type:
                            One_Line_RO_Details_copy.at[i, menu_name] = service_type
                            One_Line_RO_Details_copy.at[i, menu_name + '_items'] = int(item_count)
            
            # Get available menu from the data
            available_menu = set(One_Line_RO_Details_copy['mapped_menu'])
            
            # Check if any menu has valid values (not NaN or empty)
            if any(value and (not isinstance(value, float) or not math.isnan(value)) for value in available_menu):
                # Create indices to track rows to move
                rows_to_move = []
                
                # Identifying the menu impacted One Line RO
                for menu_map in available_menu:
                    if pd.isna(menu_map) or menu_map == '':
                        continue
                        
                    One_Line_RO_Details_with_menu = One_Line_RO_Details_copy[One_Line_RO_Details_copy[menu_map].notna()]
                    
                    for i, row in One_Line_RO_Details_with_menu.iterrows():
                        opcode = row['lbropcode']
                        mapped_menu = row['mapped_menu']
                        service_type_name = row[mapped_menu]
                        
                        # Get service_id
                        service_id = None
                        for key, value in service_type_mapping.items():
                            if value == service_type_name:
                                service_id = key
                                break
                        
                        if service_id is None:
                            continue
                        
                        if mapped_menu in One_Line_RO_Details_with_menu.columns:
                            item_count = row[mapped_menu + '_items']
                            
                            if int(item_count) > 1:
                                matching_opcode_row = assigned_menu_opcodes_df[
                                    (assigned_menu_opcodes_df['menu_opcode'] == opcode) & 
                                    (assigned_menu_opcodes_df['service_type'] == service_id) & 
                                    (assigned_menu_opcodes_df['menu_name'] == mapped_menu)
                                ]
                                
                                if not matching_opcode_row.empty:
                                    rows_to_move.append(i)
                
                # Remove rows that should be moved to Multi Line
                One_Line_RO_Details_copy = One_Line_RO_Details_copy.drop(index=rows_to_move)
        
        return One_Line_RO_Details_copy
        
    except Exception as e:
        print(f"Error in menu impact handling: {str(e)}")
        return One_Line_RO_Details

def calculate_average_open_days(combined_revenue_details):
    """Calculate average open days by pay type"""
    
    # Filter out zero-sales rows and create a proper copy to avoid SettingWithCopyWarning
    filtered_df_with_sales = combined_revenue_details[
        ~((pd.to_numeric(combined_revenue_details['lbrsale'], errors='coerce').fillna(0) == 0) &
          (pd.to_numeric(combined_revenue_details['lbrsoldhours'], errors='coerce').fillna(0) == 0) &
          (pd.to_numeric(combined_revenue_details['prtextendedsale'], errors='coerce').fillna(0) == 0) &
          (pd.to_numeric(combined_revenue_details['prtextendedcost'], errors='coerce').fillna(0) == 0))
    ].copy()

    # Calculate open days
    filtered_df_with_sales['min_opendate'] = filtered_df_with_sales.groupby('unique_ro_number')['opendate'].transform('min')
    filtered_df_with_sales['open_days'] = (pd.to_datetime(filtered_df_with_sales['closeddate']) - pd.to_datetime(filtered_df_with_sales['min_opendate'])).dt.days
    
    # Group by pay types
    pay_type_groups = {
        'C': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'C'],
        'W': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'W'],
        'I': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'I'],
        'E': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'E'],
        'M': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'M'],
        'F': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'F']
    }
    
    avg_days = {}
    for pay_type, group_df in pay_type_groups.items():
        if not group_df.empty:
            # Get unique ROs with minimum open days
            unique_ros = group_df.loc[group_df.groupby('unique_ro_number')['open_days'].idxmin()]
            ro_count = len(set(group_df['unique_ro_number']))
            avg_days[pay_type] = round_off(unique_ros['open_days'].sum() / ro_count, 2) if ro_count > 0 else 0
        else:
            avg_days[pay_type] = 0
    
    return {
        "customer_pay": avg_days.get('C', 0),
        "warranty": avg_days.get('W', 0),
        "internal": avg_days.get('I', 0),
        "extended_service": avg_days.get('E', 0),
        "maintenance": avg_days.get('M', 0),
        "factory_service": avg_days.get('F', 0)
    }

def calculate_parts_to_labor_ratio(df):
    """Calculate parts to labor ratio overall and by category"""
    
    # 930 - CP Parts to Labor Ratio - FOPC_DV_0162
    # Filtering only CP job details (matching original logic exactly)
    list_of_paytypegroup_C = df[df['paytypegroup'].isin(['C', 'M', 'E']) & (df['group'] == 'C')].to_dict('records')
    total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)

    lbr_sale_total = 0
    prt_ext_sale_total = 0

    if not total_CP_revenue_details_df.empty:
        lbr_sale_total = pd.to_numeric(total_CP_revenue_details_df['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_total = pd.to_numeric(total_CP_revenue_details_df['prtextendedsale'], errors='coerce').fillna(0).sum()
    else:
        print(" No data available for KPI Scorecard A calculation")

    overall_ratio = round_off((prt_ext_sale_total / lbr_sale_total), 2) if lbr_sale_total != 0 else 0
    
    # By category calculations (matching original logic exactly)
    total_CP_revenue_details_comp = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'COMPETITIVE']
    total_CP_revenue_details_maint = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'MAINTENANCE']
    total_CP_revenue_details_rep = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'REPAIR']

    lbr_sale_comp = 0
    prt_ext_sale_comp = 0
    if not total_CP_revenue_details_comp.empty:
        lbr_sale_comp = pd.to_numeric(total_CP_revenue_details_comp['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_comp = pd.to_numeric(total_CP_revenue_details_comp['prtextendedsale'], errors='coerce').fillna(0).sum()

    CP_Parts_to_Labor_Ratio_Comp = 0
    if lbr_sale_comp != 0:
        CP_Parts_to_Labor_Ratio_Comp = round_off((prt_ext_sale_comp / lbr_sale_comp), 2)

    lbr_sale_maint = 0
    prt_ext_sale_maint = 0
    if not total_CP_revenue_details_maint.empty:
        lbr_sale_maint = pd.to_numeric(total_CP_revenue_details_maint['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_maint = pd.to_numeric(total_CP_revenue_details_maint['prtextendedsale'], errors='coerce').fillna(0).sum()

    CP_Parts_to_Labor_Ratio_maint = 0
    if lbr_sale_maint != 0:
        CP_Parts_to_Labor_Ratio_maint = round_off((prt_ext_sale_maint / lbr_sale_maint), 2)

    lbr_sale_rep = 0
    prt_ext_sale_rep = 0
    if not total_CP_revenue_details_rep.empty:
        lbr_sale_rep = pd.to_numeric(total_CP_revenue_details_rep['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_rep = pd.to_numeric(total_CP_revenue_details_rep['prtextendedsale'], errors='coerce').fillna(0).sum()

    CP_Parts_to_Labor_Ratio_rep = 0
    if lbr_sale_rep != 0:
        CP_Parts_to_Labor_Ratio_rep = round_off((prt_ext_sale_rep / lbr_sale_rep), 2)

    return {
        "overall": overall_ratio,
        "competitive": CP_Parts_to_Labor_Ratio_Comp,
        "maintenance": CP_Parts_to_Labor_Ratio_maint,
        "repair": CP_Parts_to_Labor_Ratio_rep
    }

def calculate_comprehensive_special_metrics(combined_revenue_details):
    """Calculate all related special metrics together - matching special_metrics_filter.py exactly"""

    # 1357 - Average RO Open Days + 935 - Labor Sold Hours Percentage + 930 - Parts to Labor Ratio
    # This function processes all related metrics together to maintain data consistency

    # Add open days calculation (matching original logic exactly)
    combined_revenue_details['min_opendate'] = combined_revenue_details.groupby('unique_ro_number')['opendate'].transform('min')
    combined_revenue_details['open_days'] = (pd.to_datetime(combined_revenue_details['closeddate']) - pd.to_datetime(combined_revenue_details['min_opendate'])).dt.days

    # First filter by department and hide_ro (matching original filtered_df logic)
    filtered_df = combined_revenue_details[
        (combined_revenue_details['department'] == 'Service') &
        (combined_revenue_details['hide_ro'] != True)
    ]

    # Then filter out zero-sales rows and create proper copy (matching original filtered_df_with_sales logic)
    filtered_df_with_sales = filtered_df[
        ~((pd.to_numeric(filtered_df['lbrsale'], errors='coerce').fillna(0) == 0) &
          (pd.to_numeric(filtered_df['lbrsoldhours'], errors='coerce').fillna(0) == 0) &
          (pd.to_numeric(filtered_df['prtextendedsale'], errors='coerce').fillna(0) == 0) &
          (pd.to_numeric(filtered_df['prtextendedcost'], errors='coerce').fillna(0) == 0))
    ].copy()

    # Add open days to filtered_df_with_sales (matching original logic)
    filtered_df_with_sales['min_opendate'] = filtered_df_with_sales.groupby('unique_ro_number')['opendate'].transform('min')
    filtered_df_with_sales['open_days'] = (pd.to_datetime(filtered_df_with_sales['closeddate']) - pd.to_datetime(filtered_df_with_sales['min_opendate'])).dt.days

    # Filter data by paytypegroup from filtered_df_with_sales (matching original logic exactly)
    all_revenue_C = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'C']
    all_revenue_M = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'M']
    all_revenue_E = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'E']
    all_revenue_W = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'W']
    all_revenue_F = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'F']
    all_revenue_I = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'I']

    # Create Output directory if it doesn't exist
    output_dir = '../Output'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    all_revenue_C.to_csv('../Output/OpenDays_C.csv')

    # RO counts (matching original logic exactly)
    ro_count_for_combined = len(set(combined_revenue_details['unique_ro_number']))
    ro_count_for_C = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'C']['unique_ro_number']))
    ro_count_for_M = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'M']['unique_ro_number']))
    ro_count_for_E = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'E']['unique_ro_number']))
    ro_count_for_W = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'W']['unique_ro_number']))
    ro_count_for_F = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'F']['unique_ro_number']))
    ro_count_for_I = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'I']['unique_ro_number']))

    # Open days calculations (matching original logic exactly)
    all_revenue_without_duplicates_for_open_days_C = all_revenue_C.loc[all_revenue_C.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True) if not all_revenue_C.empty else pd.DataFrame()
    all_revenue_without_duplicates_for_open_days_W = all_revenue_W.loc[all_revenue_W.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True) if not all_revenue_W.empty else pd.DataFrame()
    all_revenue_without_duplicates_for_open_days_I = all_revenue_I.loc[all_revenue_I.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True) if not all_revenue_I.empty else pd.DataFrame()
    all_revenue_without_duplicates_for_open_days_E = all_revenue_E.loc[all_revenue_E.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True) if not all_revenue_E.empty else pd.DataFrame()
    all_revenue_without_duplicates_for_open_days_M = all_revenue_M.loc[all_revenue_M.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True) if not all_revenue_M.empty else pd.DataFrame()
    all_revenue_without_duplicates_for_open_days_F = all_revenue_F.loc[all_revenue_F.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True) if not all_revenue_F.empty else pd.DataFrame()

    if not all_revenue_without_duplicates_for_open_days_C.empty:
        all_revenue_without_duplicates_for_open_days_C.to_csv('../Output/all_revenue_without_duplicates_for_open_days_C.csv')

    open_days_sum_C = all_revenue_without_duplicates_for_open_days_C['open_days'].sum() if not all_revenue_without_duplicates_for_open_days_C.empty else 0
    open_days_sum_W = all_revenue_without_duplicates_for_open_days_W['open_days'].sum() if not all_revenue_without_duplicates_for_open_days_W.empty else 0
    open_days_sum_I = all_revenue_without_duplicates_for_open_days_I['open_days'].sum() if not all_revenue_without_duplicates_for_open_days_I.empty else 0
    open_days_sum_E = all_revenue_without_duplicates_for_open_days_E['open_days'].sum() if not all_revenue_without_duplicates_for_open_days_E.empty else 0
    open_days_sum_M = all_revenue_without_duplicates_for_open_days_M['open_days'].sum() if not all_revenue_without_duplicates_for_open_days_M.empty else 0
    open_days_sum_F = all_revenue_without_duplicates_for_open_days_F['open_days'].sum() if not all_revenue_without_duplicates_for_open_days_F.empty else 0

    # Calculate average open days (matching original logic exactly)
    avg_days_open_C = round_off((open_days_sum_C / ro_count_for_C), 2) if ro_count_for_C != 0 else 0
    avg_days_open_W = round_off((open_days_sum_W / ro_count_for_W), 2) if ro_count_for_W != 0 else 0
    avg_days_open_I = round_off((open_days_sum_I / ro_count_for_I), 2) if ro_count_for_I != 0 else 0
    avg_days_open_E = round_off((open_days_sum_E / ro_count_for_E), 2) if ro_count_for_E != 0 else 0
    avg_days_open_M = round_off((open_days_sum_M / ro_count_for_M), 2) if ro_count_for_M != 0 else 0
    avg_days_open_F = round_off((open_days_sum_F / ro_count_for_F), 2) if ro_count_for_F != 0 else 0

    # 930 - CP Parts to Labor Ratio - FOPC_DV_0162 (matching original logic exactly)
    list_of_paytypegroup_C = combined_revenue_details[combined_revenue_details['paytypegroup'].isin(['C', 'M', 'E']) & (combined_revenue_details['group'] == 'C')].to_dict('records')
    total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)

    lbr_sale_total = 0
    prt_ext_sale_total = 0

    if not total_CP_revenue_details_df.empty:
        lbr_sale_total = pd.to_numeric(total_CP_revenue_details_df['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_total = pd.to_numeric(total_CP_revenue_details_df['prtextendedsale'], errors='coerce').fillna(0).sum()
    else:
        print(" No data available for KPI Scorecard A calculation")

    CP_Parts_to_Labor_Ratio = round_off((prt_ext_sale_total / lbr_sale_total), 2) if lbr_sale_total != 0 else 0

    # By category calculations (matching original logic exactly)
    total_CP_revenue_details_comp = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'COMPETITIVE']
    total_CP_revenue_details_maint = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'MAINTENANCE']
    total_CP_revenue_details_rep = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'REPAIR']

    lbr_sale_comp = 0
    prt_ext_sale_comp = 0
    if not total_CP_revenue_details_comp.empty:
        lbr_sale_comp = pd.to_numeric(total_CP_revenue_details_comp['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_comp = pd.to_numeric(total_CP_revenue_details_comp['prtextendedsale'], errors='coerce').fillna(0).sum()

    CP_Parts_to_Labor_Ratio_Comp = round_off((prt_ext_sale_comp / lbr_sale_comp), 2) if lbr_sale_comp != 0 else 0

    lbr_sale_maint = 0
    prt_ext_sale_maint = 0
    if not total_CP_revenue_details_maint.empty:
        lbr_sale_maint = pd.to_numeric(total_CP_revenue_details_maint['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_maint = pd.to_numeric(total_CP_revenue_details_maint['prtextendedsale'], errors='coerce').fillna(0).sum()

    CP_Parts_to_Labor_Ratio_maint = round_off((prt_ext_sale_maint / lbr_sale_maint), 2) if lbr_sale_maint != 0 else 0

    lbr_sale_rep = 0
    prt_ext_sale_rep = 0
    if not total_CP_revenue_details_rep.empty:
        lbr_sale_rep = pd.to_numeric(total_CP_revenue_details_rep['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_rep = pd.to_numeric(total_CP_revenue_details_rep['prtextendedsale'], errors='coerce').fillna(0).sum()

    CP_Parts_to_Labor_Ratio_rep = round_off((prt_ext_sale_rep / lbr_sale_rep), 2) if lbr_sale_rep != 0 else 0

    # 935 - Labor Sold Hours Percentage By Pay Type - FOPC_DV_0163 (matching original logic exactly)
    All_Sold_Hours = pd.to_numeric(combined_revenue_details['lbrsoldhours'], errors='coerce').fillna(0).sum()

    # Calculate labor sold hours for each pay type (matching original logic exactly)
    labor_sold_hours_C = 0
    if not all_revenue_C.empty:
        labor_sold_hours_C = round_off((pd.to_numeric(all_revenue_C['lbrsoldhours']).fillna(0).sum()), 2)

    Labor_Sold_Hours_Percentage_C = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_C = round_off(((labor_sold_hours_C / All_Sold_Hours) * 100))

    labor_sold_hours_W = 0
    if not all_revenue_W.empty:
        labor_sold_hours_W = round_off((pd.to_numeric(all_revenue_W['lbrsoldhours']).fillna(0).sum()), 2)

    Labor_Sold_Hours_Percentage_W = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_W = round_off(((labor_sold_hours_W / All_Sold_Hours) * 100))

    labor_sold_hours_I = 0
    if not all_revenue_I.empty:
        labor_sold_hours_I = round_off((pd.to_numeric(all_revenue_I['lbrsoldhours']).fillna(0).sum()), 2)

    Labor_Sold_Hours_Percentage_I = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_I = round_off(((labor_sold_hours_I / All_Sold_Hours) * 100))
        print('All_Sold_Hours', All_Sold_Hours)
        print('labor_sold_hours_I', labor_sold_hours_I)

    labor_sold_hours_E = 0
    if not all_revenue_E.empty:
        labor_sold_hours_E = round_off((pd.to_numeric(all_revenue_E['lbrsoldhours']).fillna(0).sum()), 2)

    Labor_Sold_Hours_Percentage_E = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_E = round_off(((labor_sold_hours_E / All_Sold_Hours) * 100))

    labor_sold_hours_M = 0
    if not all_revenue_M.empty:
        labor_sold_hours_M = round_off((pd.to_numeric(all_revenue_M['lbrsoldhours']).fillna(0).sum()), 2)

    Labor_Sold_Hours_Percentage_M = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_M = round_off(((labor_sold_hours_M / All_Sold_Hours) * 100))

    labor_sold_hours_F = 0
    if not all_revenue_F.empty:
        labor_sold_hours_F = round_off((pd.to_numeric(all_revenue_F['lbrsoldhours']).fillna(0).sum()), 2)

    Labor_Sold_Hours_Percentage_F = 0
    if All_Sold_Hours != 0:
        Labor_Sold_Hours_Percentage_F = round_off(((labor_sold_hours_F / All_Sold_Hours) * 100))

    # Return all calculated metrics
    return {
        "labor_hours_percentage": {
            "customer_pay": Labor_Sold_Hours_Percentage_C,
            "warranty": Labor_Sold_Hours_Percentage_W,
            "internal": Labor_Sold_Hours_Percentage_I,
            "extended_service": Labor_Sold_Hours_Percentage_E,
            "maintenance": Labor_Sold_Hours_Percentage_M,
            "factory_service": Labor_Sold_Hours_Percentage_F
        },
        "parts_to_labor_ratio": {
            "overall": CP_Parts_to_Labor_Ratio,
            "competitive": CP_Parts_to_Labor_Ratio_Comp,
            "maintenance": CP_Parts_to_Labor_Ratio_maint,
            "repair": CP_Parts_to_Labor_Ratio_rep
        },
        "average_open_days": {
            "customer_pay": avg_days_open_C,
            "warranty": avg_days_open_W,
            "internal": avg_days_open_I,
            "extended_service": avg_days_open_E,
            "maintenance": avg_days_open_M,
            "factory_service": avg_days_open_F
        },
        "ro_counts": {
            "combined": ro_count_for_combined,
            "customer_pay": ro_count_for_C,
            "warranty": ro_count_for_W,
            "internal": ro_count_for_I,
            "extended_service": ro_count_for_E,
            "maintenance": ro_count_for_M,
            "factory_service": ro_count_for_F
        }
    }

def calculate_labor_hours_percentage(combined_revenue_details):
    """Wrapper function for backward compatibility"""
    comprehensive_metrics = calculate_comprehensive_special_metrics(combined_revenue_details)
    return comprehensive_metrics["labor_hours_percentage"]

def calculate_mpi_penetration(combined_revenue_details):
    """Calculate MPI penetration percentage"""
    try:
        # Apply the same data preprocessing as in the original code
        columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        combined_revenue_details_for_mpi = combined_revenue_details.copy()
        combined_revenue_details_for_mpi.loc[combined_revenue_details_for_mpi['opcategory'] == 'N/A', columns_to_check] = 0

        # Get MPI setup data
        MPI_setup_db_connect = MPISetupTableResult()
        MPI_setup_df = MPI_setup_db_connect.getTableResult()

        # Get MPI opcodes
        mpi_opcodes_db = MPIOpcodesTableResult()
        mpi_opcodes_list = mpi_opcodes_db.getTableResult()
        mpi_opcodes = set(mpi_opcodes_list) if mpi_opcodes_list else set()
        
        all_revenue_details_list = combined_revenue_details_for_mpi.to_dict('records')

        # Removing the jobs with 0 sales and department body shop and filter paytype group with C and W
        MPI_Opportunity_list = [
            row for row in all_revenue_details_list
            if not (
                (row['lbrsale'] == 0 and row['lbrsoldhours'] == 0 and
                 row['prtextendedsale'] == 0 and row['prtextendedcost'] == 0) or
                row['department'] == 'Body Shop' or
                row['hide_ro'] == True or
                (pd.to_numeric(row['mileage'], errors='coerce') or 0) <= 1000
            ) and row['group'] in {'C', 'W'} and row['paytypegroup'] in {'C', 'M', 'E', 'W', 'F'}
        ]

        print('MPI_Opportunity_list----->++++++++++++++++++',len(MPI_Opportunity_list))
        MPI_Opportunity_list_df = pd.DataFrame(MPI_Opportunity_list)
        # Excluding the rows with customer name blank or None
        MPI_Opportunity_list_df['customer_name'] = MPI_Opportunity_list_df['customer_name'].replace('', np.nan)
        MPI_Opportunity_list_df = MPI_Opportunity_list_df[MPI_Opportunity_list_df['customer_name'].notna()]

        # Create Output directory if it doesn't exist
        output_dir = '../Output'
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        MPI_Opportunity_list_df.to_csv('../Output/MPI_Opportunity_list_df.csv')

        Opportunities = 0
        if not MPI_Opportunity_list_df.empty:
            # Same ronumber with different closeddate will be considered as two different ronumber
            MPI_Opportunity_list_df['unique_ronumber'] = MPI_Opportunity_list_df['ronumber'].astype(str) + '_' + MPI_Opportunity_list_df['closeddate'].astype(str)
            distinct_ronumbers = set(MPI_Opportunity_list_df['unique_ronumber'])
            Opportunities = len(distinct_ronumbers)

        # MPI Completed calculation
        # Identifying the ronumber with MPI jobs
        ronumbers_with_mpi = {row['ronumber'] for row in all_revenue_details_list 
                             if row.get('lbropcode', '').strip() in mpi_opcodes}

        # Filtering all jobs of ronumber, if MPI opcodes are available in any of its job
        filtered_rows = [row for row in all_revenue_details_list if row['ronumber'] in ronumbers_with_mpi]

        # Removing jobs with zero sales and body shop jobs and jobs with paytype group 'I'
        final_filtered_rows = [
            row for row in filtered_rows
            if not (
                (row['lbrsale'] == 0 and row['lbrsoldhours'] == 0 and 
                 row['prtextendedsale'] == 0 and row['prtextendedcost'] == 0)
                or row['department'] == 'Body Shop' or row['paytypegroup'] == 'I' 
                or row['hide_ro'] == True or row['group'] == 'I'
            )
        ]

        final_filtered_rows_df = pd.DataFrame(final_filtered_rows)
        final_filtered_rows_df.to_csv('../Output/MPI_final_filtered_rows_df.csv')
        Completed_MPI_ROs_Perc = 0

        if not final_filtered_rows_df.empty:
            # Same ronumber with different closeddate will be considered as two different ronumber
            final_filtered_rows_df['unique_onumber'] = final_filtered_rows_df['ronumber'].astype(str) + '_' + final_filtered_rows_df['closeddate'].astype(str)
            Completed_MPI_ROs = set(final_filtered_rows_df['unique_onumber'])
            Completed_MPI_ROs_Perc = round_off(((len(Completed_MPI_ROs) / Opportunities) * 100), 2) if Opportunities > 0 else 0

        return Completed_MPI_ROs_Perc

    except Exception as e:
        print(f"Error calculating MPI penetration: {str(e)}")
        return 0

def calculate_menu_penetration(combined_revenue_details):
    """Calculate menu penetration percentage"""
    try:
        # Apply the same data preprocessing as in the original code
        columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        combined_revenue_details_for_menu = combined_revenue_details.copy()
        combined_revenue_details_for_menu.loc[combined_revenue_details_for_menu['opcategory'] == 'N/A', columns_to_check] = 0

        # Filter for valid opportunities - exactly matching original logic
        all_revenue_details_list = combined_revenue_details_for_menu.to_dict('records')
        
        # Removing the jobs with department body shop and filter paytype group with C and W
        Menu_Opportunity_list = [
            row for row in all_revenue_details_list
            if not (row['department'] == 'Body Shop') and row['group'] in {'C', 'W'} 
            and row['paytypegroup'] in {'C', 'M', 'E', 'W', 'F'}
        ]

        Menu_Opportunity_list_df = pd.DataFrame(Menu_Opportunity_list)
        
        # Excluding the rows with mileage less than 1000 and customer_name is blank or None
        Menu_Opportunity_list_df['mileage_numeric'] = pd.to_numeric(Menu_Opportunity_list_df['mileage'], errors='coerce').fillna(0)
        Menu_Opportunity_list_df = Menu_Opportunity_list_df[Menu_Opportunity_list_df['mileage_numeric'] > 1000]
        Menu_Opportunity_list_df['customer_name'] = Menu_Opportunity_list_df['customer_name'].replace('', np.nan)
        Menu_Opportunity_list_df = Menu_Opportunity_list_df[Menu_Opportunity_list_df['customer_name'].notna()]

        # Create Output directory if it doesn't exist
        output_dir = '../Output'
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        Menu_Opportunity_list_df.to_csv('../Output/total_revenue_details_C_W.csv')

        if Menu_Opportunity_list_df.empty:
            return 0

        # Get menu setup data
        menu_master_db = menuMasterTableResult()
        menu_master_df = menu_master_db.getTableResult()
        
        assigned_menu_opcodes_db = assignedMenuOpcodesTableResult()
        assigned_menu_opcodes_df = assigned_menu_opcodes_db.getTableResult()
        
        if menu_master_df.empty or assigned_menu_opcodes_df.empty:
            return 0

        # Same ronumber with different closeddate will be considered as two different ronumber
        Menu_Opportunity_list_df['unique_ronumber'] = Menu_Opportunity_list_df['ronumber'].astype(str) + '_' + Menu_Opportunity_list_df['closeddate'].astype(str)
        opportunities = Menu_Opportunity_list_df['unique_ronumber'].nunique()

        if opportunities == 0:
            return 0

        # Get all menu opcodes
        menu_opcodes = set(assigned_menu_opcodes_df['menu_opcode'].str.strip())

        # Filter ROs with menu opcodes - matching original logic exactly
        ronumbers_with_menu_opcodes = set()
        for _, row in combined_revenue_details_for_menu.iterrows():
            if row.get('lbropcode', '').strip() in menu_opcodes:
                ronumbers_with_menu_opcodes.add(row['ronumber'])

        menu_ros = Menu_Opportunity_list_df[
            Menu_Opportunity_list_df['ronumber'].isin(ronumbers_with_menu_opcodes)
        ]
        
        # Further filter for customer pay jobs only
        menu_ros = menu_ros[
            (menu_ros['group'] == 'C') &
            (menu_ros['paytypegroup'].isin(['C', 'M', 'E']))
        ]

        # Get unique menu RO count
        menu_sold = menu_ros['unique_ronumber'].nunique()

        # Calculate percentage
        menu_penetration = round_off((menu_sold / opportunities) * 100, 2)
        return menu_penetration

    except Exception as e:
        print(f"Error calculating menu penetration: {str(e)}")
        return 0

def initialize_empty_metrics():
    """Initialize empty metrics structure"""
    return {
        "one_line_metrics": {
            "under_60k": 0, "over_60k": 0, "total_shop": 0,
            "perc_under_60k": 0, "perc_over_60k": 0, "perc_total_shop": 0
        },
        "multi_line_metrics": {
            "under_60k": 0, "over_60k": 0, "total_shop": 0,
            "perc_under_60k": 0, "perc_over_60k": 0, "perc_total_shop": 0
        },
        "average_open_days": {
            "customer_pay": 0, "warranty": 0, "internal": 0,
            "extended_service": 0, "maintenance": 0, "factory_service": 0
        },
        "parts_to_labor_ratio": {
            "overall": 0, "competitive": 0, "maintenance": 0, "repair": 0
        },
        "labor_hours_percentage": {
            "customer_pay": 0, "warranty": 0, "internal": 0,
            "extended_service": 0, "maintenance": 0, "factory_service": 0
        },
        "mpi_penetration_percentage": 0,
        "menu_penetration_percentage": 0
    }

def db_execution_special_metrics(target_date_str, advisor, tech, retail_flag, columns_to_check):
        """Handle database operations and execute special metrics processing"""
        
        try:
            # Get target month date range
            month_start, month_end = get_month_date_range_from_target(target_date_str)
            
            # Fetch all data from database
            all_revenue_details_table_db_connect = allRevenueDetailsTable()
            all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()
            
            if all_revenue_details_df.empty:
                return None
            
            # Convert date column properly
            print(f"Debug - DataFrame shape before date conversion: {all_revenue_details_df.shape}")
            print(f"Debug - Sample closeddate values: {all_revenue_details_df['closeddate'].head()}")
            print(f"Debug - closeddate dtype before conversion: {all_revenue_details_df['closeddate'].dtype}")

            all_revenue_details_df['closeddate'] = pd.to_datetime(all_revenue_details_df['closeddate'], errors='coerce')
            print(f"Debug - closeddate dtype after conversion: {all_revenue_details_df['closeddate'].dtype}")
            print(f"Debug - Any null dates after conversion: {all_revenue_details_df['closeddate'].isnull().sum()}")

            # Convert other numeric columns and preprocess
            columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
            all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
            all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))
            
            # Define customer and warranty pay types based on retail_flag
            if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
                customer_pay_types = {'C'}
                warranty_pay_types = {'W', 'F', 'M', 'E'}
            elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
                customer_pay_types = {'C', 'M'}
                warranty_pay_types = {'W', 'F', 'E'}
            elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
                customer_pay_types = {'C', 'E'}
                warranty_pay_types = {'W', 'F', 'M'}
            elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
                customer_pay_types = {'C', 'E', 'M'}
                warranty_pay_types = {'W', 'F'}
            
            target_month_result = process_target_month_special_metrics(
                all_revenue_details_df, 
                month_start, 
                month_end,            
                advisor, 
                tech, 
                retail_flag, 
                customer_pay_types, 
                warranty_pay_types, 
                columns_to_check
            )
            
            return target_month_result, customer_pay_types, warranty_pay_types
            
        except Exception as e:
            print(f"ERROR in db_execution_special_metrics: {str(e)}")
            return None, None, None

def db_calculation_special_metrics():
        """Main execution function for special metrics calculation"""

        # Configuration variables
        columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        retail_flag = {'C','M'}

        # Get configuration from global config object
        advisor_set = config.advisor if hasattr(config, 'advisor') else 'all'
        tech_set = config.technician if hasattr(config, 'technician') else 'all'
        TARGET_MONTHS_YEARS = config.target_month_year
        
        # Process advisor configuration
        if isinstance(advisor_set, str):
            if advisor_set.lower() == 'all':
                advisor = {'all'}
            elif ',' in advisor_set:
                advisor = {x.strip() for x in advisor_set.split(',')}
            else:
                advisor = {advisor_set.strip()}
        else:
            advisor = {'all'}
        
        # Process technician configuration
        if isinstance(tech_set, str):
            if tech_set.lower() == 'all':
                tech = {'all'}
            elif ',' in tech_set:
                tech = {x.strip() for x in tech_set.split(',')}
            else:
                tech = {tech_set.strip()}
        else:
            tech = {'all'}
        
        # Execute database operations and processing
        target_date_str = TARGET_MONTHS_YEARS[0]
        target_month_result, customer_pay_types, warranty_pay_types = db_execution_special_metrics(
            target_date_str, advisor, tech, retail_flag, columns_to_check
        )
        
        # Process results
        if target_month_result:
            final_result_set = {
                "analysis_info": {
                    "target_month": target_date_str,
                    "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "advisor_filter": list(advisor),
                    "technician_filter": list(tech),
                    "customer_pay_types": list(customer_pay_types),
                    "warranty_pay_types": list(warranty_pay_types)
                },
                "target_month_results": target_month_result
            }
            
            # Create directory if it doesn't exist
            os.makedirs("chart_processing_results", exist_ok=True)
            
            # Write results to JSON file
            output_filename = "chart_processing_results/special_metrics_calculated_value.json"
            with open(output_filename, 'w', encoding='utf-8') as json_file:
                json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)
            
            print(f"\nSpecial metrics data written successfully to {output_filename}")
            
            # Display summary
            print(f"\nSpecial Metrics Summary for {target_month_result['target_month_name']}:")
            print(f"  Total ROs: {target_month_result['total_ros']}")
            print(f"  One-Line ROs: {target_month_result['special_metrics']['one_line_metrics']['total_shop']}")
            print(f"  Multi-Line ROs: {target_month_result['special_metrics']['multi_line_metrics']['total_shop']}")
            print(f"  Parts to Labor Ratio: {target_month_result['special_metrics']['parts_to_labor_ratio']['overall']}")
            
        else:
            print(f"No data available for target month {target_date_str}")

def parse_arguments():
        """Parse command line arguments"""
        parser = argparse.ArgumentParser(description="Run special metrics validation with store context.")

        parser.add_argument("--store_id", required=True, help="Store ID")
        parser.add_argument("--store_name", required=True, help="Store Name")
        parser.add_argument("--start_date", required=True, help="Start date (YYYY-MM-DD)")
        parser.add_argument("--end_date", required=True, help="End date (YYYY-MM-DD)")
        parser.add_argument("--fopc_month", required=True, help="Current FOPC month being evaluated (format: YYYY-MM)")
        parser.add_argument("--pre_fopc_month", required=True, help="Previous FOPC month for comparison (format: YYYY-MM)")
        parser.add_argument("--database_name", required=True, help="Name of the database to connect (e.g., sampackag)")
        parser.add_argument("--working_days", required=True, help="Number of working days in the selected date range (e.g., 46.74)")
        parser.add_argument("--advisor", required=True, help="Advisor name or 'all' to include all advisors")
        parser.add_argument("--technician", required=True, help="Technician name or 'all' to include all technicians")
        parser.add_argument("--site_url", required=True, help="Base URL of the site (e.g., https://sampackag.fixedops.cc/)")
        parser.add_argument("--last_month", required=True, help="Last month for client report card three months (format: YYYY-MM)")
        parser.add_argument("--role", required=True, help="Name of role")
        parser.add_argument("--target_month_year", required=True, help="Target month/year, e.g. 2023-11-01")

        return parser.parse_args()

def set_config_from_args(args):
        """Set global config from command line arguments"""
        config.store_id = args.store_id
        config.store_name = args.store_name
        config.start_date = args.start_date
        config.end_date = args.end_date
        config.fopc_month = args.fopc_month
        config.pre_fopc_month = args.pre_fopc_month
        config.database_name = args.database_name
        config.working_days = float(args.working_days)
        config.advisor = args.advisor
        config.technician = args.technician
        config.site_url = args.site_url
        config.last_month = args.last_month
        config.role = args.role

        # Convert target_month_year from YYYY-MM-DD to YYYY-MM format
        target_date = args.target_month_year
        if len(target_date) == 10:  # YYYY-MM-DD format
            target_date = target_date[:7]  # Convert to YYYY-MM
        config.target_month_year = [target_date]

# Example usage
if __name__ == "__main__":
    try:
        # Parse command line arguments
        args = parse_arguments()
        set_config_from_args(args)
        db_calculation_special_metrics()
    except SystemExit:
        # If no arguments provided, use default configuration for testing
        print("No arguments provided, using default configuration for testing...")
        config.store_id = "244284397"
        config.store_name = "Carriage Kia of Woodstock"
        config.start_date = "2025-04-01"
        config.end_date = "2025-07-30"
        config.fopc_month = "2025-04"
        config.pre_fopc_month = "2025-01"
        config.database_name = "fopc_simt_prime"
        config.working_days = 73.8
        config.advisor = "all"
        config.technician = "all"
        config.site_url = "https://carriageag-simt.fixedopspc.com"
        config.last_month = "2025-07"
        config.role = "Admin"
        config.target_month_year = ["2025-07"]

        db_calculation_special_metrics()