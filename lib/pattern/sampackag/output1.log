/home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/sampackag/validate_spcl_db_metrics.py:675: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  filtered_df_with_sales['min_opendate'] = filtered_df_with_sales.groupby('unique_ro_number')['opendate'].transform('min')
/home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/sampackag/validate_spcl_db_metrics.py:676: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  filtered_df_with_sales['open_days'] = (pd.to_datetime(filtered_df_with_sales['closeddate']) - pd.to_datetime(filtered_df_with_sales['min_opendate'])).dt.days
/home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/sampackag/validate_spcl_db_metrics.py:771: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  mpi_opportunities['unique_ronumber'] = mpi_opportunities['ronumber'].astype(str) + '_' + mpi_opportunities['closeddate'].astype(str)
/home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/sampackag/validate_spcl_db_metrics.py:793: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  mpi_ros['unique_ronumber'] = mpi_ros['ronumber'].astype(str) + '_' + mpi_ros['closeddate'].astype(str)
DEBUG - allRevenueDetailsTableQuery parameters:
  store_id_sql: ('244284397')
  start_date: 2025-04-01
  end_date: 2025-07-31
  Generated query: SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id IN ('244284397') AND realm = 'carriageag' AND closeddate >= '2025-04-01' AND closeddate <= '2025-07-31';
Target month range: 2025-07-01 to 2025-07-31
Filtered data shape ============================: ronumber                      3225
open_month                    3225
opendate                      3225
month_year                    3225
closeddate                    3225
vin                           3225
lbrlinecode                   3225
lbrsequenceno                 3225
paytype                       3225
paytypegroup                  3225
lbropcode                     3225
opcategory                    3225
opsubcategory                 3225
lbropcodedesc                 3225
lbrsale                       3225
lbrcost                       3225
lbrsoldhours                  3225
lbractualhours                2514
filter_by_revenue             3225
prtextendedsale               3225
prtextendedcost               3225
filter_by_laborparts          3225
linaddonflag                     0
serviceadvisor                3225
lbrtechno                     3225
lbrtechhours                  2493
lbrgrossprofit                2514
prtsgrossprofit               3225
elr                           1844
markup                        1343
prts_grossprofitpercentage    1343
lbr_grossprofitpercentage     1850
department                    3225
store_id                      3225
lbrsequenceno_idx             3225
make                          3225
advisor_name                  3225
advisor_active                3225
model                         3225
year                          3225
customer_name                 3225
mileage                       3225
lbrtechname                   3225
lbrtech_active                3225
clientid                         0
customer_no                   3225
hide_ro                       3225
realm                         3225
dtype: int64
Error in menu mapping logic: '<=' not supported between instances of 'decimal.Decimal' and 'str'
one_line_below60k_lbrsale vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv: 30079.29
calculate_parts_to_labor_ratio=======================---->total_labor_sale  134481.75
calculate_parts_to_labor_ratio=======================---->total_parts_sale  128526.14
calculate_parts_to_labor_ratio=======================---->overall_ratio  0.96
Debug - calculate_mpi_penetration: MPI opportunities kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk      ronumber open_month    opendate  ...       realm    unique_ro_number group
4     5709496    2025-06  2025-06-19  ...  carriageag  5709496_2025-07-01     C
5     5709496    2025-06  2025-06-19  ...  carriageag  5709496_2025-07-01     C
6     5709496    2025-06  2025-06-19  ...  carriageag  5709496_2025-07-01     C
7     5709496    2025-06  2025-06-19  ...  carriageag  5709496_2025-07-01     C
8     5709496    2025-06  2025-06-19  ...  carriageag  5709496_2025-07-01     C
...       ...        ...         ...  ...         ...                 ...   ...
3220  5711354    2025-07  2025-07-22  ...  carriageag  5711354_2025-07-22     C
3221  5711357    2025-07  2025-07-22  ...  carriageag  5711357_2025-07-22     C
3222  5711357    2025-07  2025-07-22  ...  carriageag  5711357_2025-07-22     C
3223  5711360    2025-07  2025-07-22  ...  carriageag  5711360_2025-07-22     C
3224  5711360    2025-07  2025-07-22  ...  carriageag  5711360_2025-07-22     C

[2552 rows x 50 columns]
Debug - calculate_mpi_penetration: Opportunities vvvvvvvvvvvvvvvvvvvvvvvvvvv 811
Debug - calculate_mpi_penetration: MPI opcodes DB 1111111111111111111111----------- <lib.pattern.sampackag.db_handler.db_connector.MPIOpcodesTableResult object at 0x7f6c76539f10>
Debug - calculate_mpi_penetration: MPI opcodes 222222222222222222222222---------- {'MPI', '*'}
Debug - calculate_mpi_penetration: MPI penetration 333333333333333333-------- 87.67
Debug - calculate_menu_penetration: Entering function
Error calculating menu penetration: '<=' not supported between instances of 'decimal.Decimal' and 'str'
Error loading total_details: totalDetailsTable class not available
Warning: totalshopsupply data not available. Using placeholder values.
Warning: No shop supplies data available. Using placeholder values.

Special metrics data written successfully to chart_processing_results/special_metrics_calculated_value.json

Special Metrics Summary for July 2025:
  Total ROs: 1144
  One-Line ROs: 419
  Multi-Line ROs: 242
  Parts to Labor Ratio: 0.96
